import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';

// Import screens
import HomeScreen from '../screens/HomeScreen';
import MapScreen from '../screens/MapScreen';
import TagsScreen from '../screens/TagsScreen';
import SettingsScreen from '../screens/SettingsScreen';
import CreateTagScreen from '../screens/CreateTagScreen';
import TagDetailScreen from '../screens/TagDetailScreen';
import MusicServiceScreen from '../screens/MusicServiceScreen';
import PlaybackScreen from '../screens/PlaybackScreen';

// Type definitions for navigation
export type RootStackParamList = {
  MainTabs: undefined;
  CreateTag: undefined;
  TagDetail: { tagId: string };
  MusicService: { service: string };
  Playback: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Map: undefined;
  Tags: undefined;
  Settings: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

// Main Tab Navigator
function MainTabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'Home':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'Map':
              iconName = focused ? 'map' : 'map-outline';
              break;
            case 'Tags':
              iconName = focused ? 'bookmark' : 'bookmark-outline';
              break;
            case 'Settings':
              iconName = focused ? 'settings' : 'settings-outline';
              break;
            default:
              iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#007AFF',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeScreen}
        options={{ title: 'Home' }}
      />
      <Tab.Screen 
        name="Map" 
        component={MapScreen}
        options={{ title: 'Map' }}
      />
      <Tab.Screen 
        name="Tags" 
        component={TagsScreen}
        options={{ title: 'My Tags' }}
      />
      <Tab.Screen 
        name="Settings" 
        component={SettingsScreen}
        options={{ title: 'Settings' }}
      />
    </Tab.Navigator>
  );
}

// Root Stack Navigator
export default function AppNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#007AFF',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen 
        name="MainTabs" 
        component={MainTabNavigator}
        options={{ headerShown: false }}
      />
      <Stack.Screen 
        name="CreateTag" 
        component={CreateTagScreen}
        options={{ 
          title: 'Create Location Tag',
          presentation: 'modal',
        }}
      />
      <Stack.Screen 
        name="TagDetail" 
        component={TagDetailScreen}
        options={{ title: 'Tag Details' }}
      />
      <Stack.Screen 
        name="MusicService" 
        component={MusicServiceScreen}
        options={{ title: 'Connect Music Service' }}
      />
      <Stack.Screen 
        name="Playback" 
        component={PlaybackScreen}
        options={{ 
          title: 'Now Playing',
          presentation: 'modal',
        }}
      />
    </Stack.Navigator>
  );
}
