{"version": 3, "sources": ["Gestures.ts"], "names": ["Gestures", "NativeViewGestureHandler", "PanGestureHandler", "TapGestureHandler", "LongPressGestureHandler", "PinchGestureHandler", "RotationGestureHandler", "FlingGestureHandler", "ManualGestureHandler", "HoverGestureHandler", "HammerGestures", "HammerNativeViewGestureHandler", "HammerPanGestureHandler", "HammerTapGestureHandler", "HammerLongPressGestureHandler", "HammerPinchGestureHandler", "HammerRotationGestureHandler", "HammerFlingGestureHandler"], "mappings": ";;;;;;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAGA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;AAlBA;AAWA;AASO,MAAMA,QAAQ,GAAG;AACtBC,EAAAA,wBAAwB,EAAxBA,iCADsB;AAEtBC,EAAAA,iBAAiB,EAAjBA,0BAFsB;AAGtBC,EAAAA,iBAAiB,EAAjBA,0BAHsB;AAItBC,EAAAA,uBAAuB,EAAvBA,gCAJsB;AAKtBC,EAAAA,mBAAmB,EAAnBA,4BALsB;AAMtBC,EAAAA,sBAAsB,EAAtBA,+BANsB;AAOtBC,EAAAA,mBAAmB,EAAnBA,4BAPsB;AAQtBC,EAAAA,oBAAoB,EAApBA,6BARsB;AAStBC,EAAAA,mBAAmB,EAAnBA;AATsB,CAAjB;;AAYA,MAAMC,cAAc,GAAG;AAC5BT,EAAAA,wBAAwB,EAAEU,kCADE;AAE5BT,EAAAA,iBAAiB,EAAEU,2BAFS;AAG5BT,EAAAA,iBAAiB,EAAEU,2BAHS;AAI5BT,EAAAA,uBAAuB,EAAEU,iCAJG;AAK5BT,EAAAA,mBAAmB,EAAEU,6BALO;AAM5BT,EAAAA,sBAAsB,EAAEU,gCANI;AAO5BT,EAAAA,mBAAmB,EAAEU;AAPO,CAAvB", "sourcesContent": ["// Gesture Handlers\nimport PanGestureHandler from './handlers/PanGestureHandler';\nimport TapGestureHandler from './handlers/TapGestureHandler';\nimport LongPressGestureHandler from './handlers/LongPressGestureHandler';\nimport PinchGestureHandler from './handlers/PinchGestureHandler';\nimport RotationGestureHandler from './handlers/RotationGestureHandler';\nimport FlingGestureHandler from './handlers/FlingGestureHandler';\nimport NativeViewGestureHandler from './handlers/NativeViewGestureHandler';\nimport ManualGestureHandler from './handlers/ManualGestureHandler';\nimport HoverGestureHandler from './handlers/HoverGestureHandler';\n\n// Hammer Handlers\nimport HammerNativeViewGestureHandler from '../web_hammer/NativeViewGestureHandler';\nimport HammerPanGestureHandler from '../web_hammer/PanGestureHandler';\nimport HammerTapGestureHandler from '../web_hammer/TapGestureHandler';\nimport HammerLongPressGestureHandler from '../web_hammer/LongPressGestureHandler';\nimport HammerPinchGestureHandler from '../web_hammer/PinchGestureHandler';\nimport HammerRotationGestureHandler from '../web_hammer/RotationGestureHandler';\nimport HammerFlingGestureHandler from '../web_hammer/FlingGestureHandler';\n\nexport const Gestures = {\n  NativeViewGestureHandler,\n  PanGestureHandler,\n  TapGestureHandler,\n  LongPressGestureHandler,\n  PinchGestureHandler,\n  RotationGestureHandler,\n  FlingGestureHandler,\n  ManualGestureHandler,\n  HoverGestureHandler,\n};\n\nexport const HammerGestures = {\n  NativeViewGestureHandler: HammerNativeViewGestureHandler,\n  PanGestureHandler: HammerPanGestureHandler,\n  TapGestureHandler: HammerTapGestureHandler,\n  LongPressGestureHandler: HammerLongPressGestureHandler,\n  PinchGestureHandler: HammerPinchGestureHandler,\n  RotationGestureHandler: HammerRotationGestureHandler,\n  FlingGestureHandler: HammerFlingGestureHandler,\n};\n"]}