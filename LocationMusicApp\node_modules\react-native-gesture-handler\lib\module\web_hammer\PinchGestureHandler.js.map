{"version": 3, "sources": ["PinchGestureHandler.ts"], "names": ["Hammer", "IndiscreteGestureHandler", "PinchGestureHandler", "name", "NativeGestureClass", "Pinch", "transformNativeEvent", "scale", "velocity", "center", "focalX", "x", "focalY", "y"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,gBAAnB;AAGA,OAAOC,wBAAP,MAAqC,4BAArC;;AAEA,MAAMC,mBAAN,SAAkCD,wBAAlC,CAA2D;AACjD,MAAJE,IAAI,GAAG;AACT,WAAO,OAAP;AACD;;AAEqB,MAAlBC,kBAAkB,GAAG;AACvB,WAAOJ,MAAM,CAACK,KAAd;AACD;;AAEDC,EAAAA,oBAAoB,CAAC;AAAEC,IAAAA,KAAF;AAASC,IAAAA,QAAT;AAAmBC,IAAAA;AAAnB,GAAD,EAA8C;AAChE,WAAO;AACLC,MAAAA,MAAM,EAAED,MAAM,CAACE,CADV;AAELC,MAAAA,MAAM,EAAEH,MAAM,CAACI,CAFV;AAGLL,MAAAA,QAHK;AAILD,MAAAA;AAJK,KAAP;AAMD;;AAhBwD;;AAmB3D,eAAeL,mBAAf", "sourcesContent": ["import Hammer from '@egjs/hammerjs';\nimport { HammerInputExt } from './GestureHandler';\n\nimport IndiscreteGestureHandler from './IndiscreteGestureHandler';\n\nclass PinchGestureHandler extends IndiscreteGestureHandler {\n  get name() {\n    return 'pinch';\n  }\n\n  get NativeGestureClass() {\n    return Hammer.Pinch;\n  }\n\n  transformNativeEvent({ scale, velocity, center }: HammerInputExt) {\n    return {\n      focalX: center.x,\n      focalY: center.y,\n      velocity,\n      scale,\n    };\n  }\n}\n\nexport default PinchGestureHandler;\n"]}