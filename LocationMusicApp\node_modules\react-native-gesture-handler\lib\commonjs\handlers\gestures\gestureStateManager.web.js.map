{"version": 3, "sources": ["gestureStateManager.web.ts"], "names": ["GestureStateManager", "create", "handlerTag", "begin", "NodeManager", "<PERSON><PERSON><PERSON><PERSON>", "activate", "fail", "end"], "mappings": ";;;;;;;AAAA;;;;AAGO,MAAMA,mBAAmB,GAAG;AACjCC,EAAAA,MAAM,CAACC,UAAD,EAA8C;AAClD,WAAO;AACLC,MAAAA,KAAK,EAAE,MAAM;AACXC,6BAAYC,UAAZ,CAAuBH,UAAvB,EAAmCC,KAAnC;AACD,OAHI;AAKLG,MAAAA,QAAQ,EAAE,MAAM;AACdF,6BAAYC,UAAZ,CAAuBH,UAAvB,EAAmCI,QAAnC,CAA4C,IAA5C;AACD,OAPI;AASLC,MAAAA,IAAI,EAAE,MAAM;AACVH,6BAAYC,UAAZ,CAAuBH,UAAvB,EAAmCK,IAAnC;AACD,OAXI;AAaLC,MAAAA,GAAG,EAAE,MAAM;AACTJ,6BAAYC,UAAZ,CAAuBH,UAAvB,EAAmCM,GAAnC;AACD;AAfI,KAAP;AAiBD;;AAnBgC,CAA5B", "sourcesContent": ["import NodeManager from '../../web/tools/NodeManager';\nimport { GestureStateManagerType } from './gestureStateManager';\n\nexport const GestureStateManager = {\n  create(handlerTag: number): GestureStateManagerType {\n    return {\n      begin: () => {\n        NodeManager.getHandler(handlerTag).begin();\n      },\n\n      activate: () => {\n        NodeManager.getHandler(handlerTag).activate(true);\n      },\n\n      fail: () => {\n        NodeManager.getHandler(handlerTag).fail();\n      },\n\n      end: () => {\n        NodeManager.getHandler(handlerTag).end();\n      },\n    };\n  },\n};\n"]}