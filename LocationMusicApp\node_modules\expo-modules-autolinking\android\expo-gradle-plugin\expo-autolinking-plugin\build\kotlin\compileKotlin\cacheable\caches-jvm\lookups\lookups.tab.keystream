  CommonExtension com.android.build.api.dsl  srcDir 3com.android.build.api.dsl.AndroidSourceDirectorySet  java *com.android.build.api.dsl.AndroidSourceSet  
sourceSets )com.android.build.api.dsl.CommonExtension  AndroidComponentsExtension com.android.build.api.variant  finalizeDsl 8com.android.build.api.variant.AndroidComponentsExtension  	dependsOn /com.android.build.gradle.internal.tasks.factory  AndroidComponentsExtension expo.modules.plugin  Any expo.modules.plugin  AutolinkigCommandBuilder expo.modules.plugin  AutolinkingOptions expo.modules.plugin  Colors expo.modules.plugin  ExpoAutolinkingPlugin expo.modules.plugin  ExpoGradleExtension expo.modules.plugin  ExpoRootProjectPlugin expo.modules.plugin  GeneratePackagesListTask expo.modules.plugin  IllegalStateException expo.modules.plugin  Integer expo.modules.plugin  	KSPLookup expo.modules.plugin  List expo.modules.plugin  Paths expo.modules.plugin  String expo.modules.plugin  Task expo.modules.plugin  Unit expo.modules.plugin  VersionCatalogsExtension expo.modules.plugin  defineDefaultProperties expo.modules.plugin  	dependsOn expo.modules.plugin  extra expo.modules.plugin  forEach expo.modules.plugin  generatedFilesSrcDir expo.modules.plugin  generatedPackageListFilename expo.modules.plugin  generatedPackageListNamespace expo.modules.plugin  	getOrNull expo.modules.plugin  getValue expo.modules.plugin  getVersionOrDefault expo.modules.plugin  invoke expo.modules.plugin  java expo.modules.plugin  mapOf expo.modules.plugin  	partition expo.modules.plugin  replace expo.modules.plugin  requireNotNull expo.modules.plugin  
setIfNotExist expo.modules.plugin  to expo.modules.plugin  
trimIndent expo.modules.plugin  with expo.modules.plugin  	withColor expo.modules.plugin  withSubproject expo.modules.plugin  withSubprojects expo.modules.plugin  build ,expo.modules.plugin.AutolinkigCommandBuilder  command ,expo.modules.plugin.AutolinkigCommandBuilder  option ,expo.modules.plugin.AutolinkigCommandBuilder  useAutolinkingOptions ,expo.modules.plugin.AutolinkigCommandBuilder  invoke 6expo.modules.plugin.AutolinkigCommandBuilder.Companion  fromJson &expo.modules.plugin.AutolinkingOptions  toJson &expo.modules.plugin.AutolinkingOptions  fromJson 0expo.modules.plugin.AutolinkingOptions.Companion  AndroidComponentsExtension )expo.modules.plugin.ExpoAutolinkingPlugin  AutolinkingOptions )expo.modules.plugin.ExpoAutolinkingPlugin  Colors )expo.modules.plugin.ExpoAutolinkingPlugin  	Directory )expo.modules.plugin.ExpoAutolinkingPlugin  ExpoGradleExtension )expo.modules.plugin.ExpoAutolinkingPlugin  GeneratePackagesListTask )expo.modules.plugin.ExpoAutolinkingPlugin  IllegalStateException )expo.modules.plugin.ExpoAutolinkingPlugin  Paths )expo.modules.plugin.ExpoAutolinkingPlugin  Project )expo.modules.plugin.ExpoAutolinkingPlugin  Provider )expo.modules.plugin.ExpoAutolinkingPlugin  RegularFile )expo.modules.plugin.ExpoAutolinkingPlugin  String )expo.modules.plugin.ExpoAutolinkingPlugin  Task )expo.modules.plugin.ExpoAutolinkingPlugin  TaskProvider )expo.modules.plugin.ExpoAutolinkingPlugin  createGeneratePackagesListTask )expo.modules.plugin.ExpoAutolinkingPlugin  	dependsOn )expo.modules.plugin.ExpoAutolinkingPlugin  generatedFilesSrcDir )expo.modules.plugin.ExpoAutolinkingPlugin  generatedPackageListFilename )expo.modules.plugin.ExpoAutolinkingPlugin  generatedPackageListNamespace )expo.modules.plugin.ExpoAutolinkingPlugin  getDEPENDSOn )expo.modules.plugin.ExpoAutolinkingPlugin  getDependsOn )expo.modules.plugin.ExpoAutolinkingPlugin  getGENERATEDFilesSrcDir )expo.modules.plugin.ExpoAutolinkingPlugin  getGENERATEDPackageListFilename )expo.modules.plugin.ExpoAutolinkingPlugin   getGENERATEDPackageListNamespace )expo.modules.plugin.ExpoAutolinkingPlugin  getGeneratedFilesSrcDir )expo.modules.plugin.ExpoAutolinkingPlugin  getGeneratedPackageListFilename )expo.modules.plugin.ExpoAutolinkingPlugin   getGeneratedPackageListNamespace )expo.modules.plugin.ExpoAutolinkingPlugin  getPARTITION )expo.modules.plugin.ExpoAutolinkingPlugin  getPackageListDir )expo.modules.plugin.ExpoAutolinkingPlugin  getPackageListFile )expo.modules.plugin.ExpoAutolinkingPlugin  getPartition )expo.modules.plugin.ExpoAutolinkingPlugin  
getREPLACE )expo.modules.plugin.ExpoAutolinkingPlugin  getREQUIRENotNull )expo.modules.plugin.ExpoAutolinkingPlugin  
getReplace )expo.modules.plugin.ExpoAutolinkingPlugin  getRequireNotNull )expo.modules.plugin.ExpoAutolinkingPlugin  getWITHColor )expo.modules.plugin.ExpoAutolinkingPlugin  getWITHSubprojects )expo.modules.plugin.ExpoAutolinkingPlugin  getWithColor )expo.modules.plugin.ExpoAutolinkingPlugin  getWithSubprojects )expo.modules.plugin.ExpoAutolinkingPlugin  java )expo.modules.plugin.ExpoAutolinkingPlugin  	partition )expo.modules.plugin.ExpoAutolinkingPlugin  replace )expo.modules.plugin.ExpoAutolinkingPlugin  requireNotNull )expo.modules.plugin.ExpoAutolinkingPlugin  	withColor )expo.modules.plugin.ExpoAutolinkingPlugin  withSubprojects )expo.modules.plugin.ExpoAutolinkingPlugin  config 'expo.modules.plugin.ExpoGradleExtension  hash 'expo.modules.plugin.ExpoGradleExtension  options 'expo.modules.plugin.ExpoGradleExtension  Project )expo.modules.plugin.ExpoRootProjectPlugin  VersionCatalogsExtension )expo.modules.plugin.ExpoRootProjectPlugin  defineDefaultProperties )expo.modules.plugin.ExpoRootProjectPlugin  getWITH )expo.modules.plugin.ExpoRootProjectPlugin  getWith )expo.modules.plugin.ExpoRootProjectPlugin  java )expo.modules.plugin.ExpoRootProjectPlugin  with )expo.modules.plugin.ExpoRootProjectPlugin  AutolinkigCommandBuilder ,expo.modules.plugin.GeneratePackagesListTask  AutolinkingOptions ,expo.modules.plugin.GeneratePackagesListTask  Input ,expo.modules.plugin.GeneratePackagesListTask  
OutputFile ,expo.modules.plugin.GeneratePackagesListTask  Property ,expo.modules.plugin.GeneratePackagesListTask  RegularFileProperty ,expo.modules.plugin.GeneratePackagesListTask  String ,expo.modules.plugin.GeneratePackagesListTask  commandLine ,expo.modules.plugin.GeneratePackagesListTask  getGROUP ,expo.modules.plugin.GeneratePackagesListTask  getGroup ,expo.modules.plugin.GeneratePackagesListTask  
getWORKINGDir ,expo.modules.plugin.GeneratePackagesListTask  
getWorkingDir ,expo.modules.plugin.GeneratePackagesListTask  group ,expo.modules.plugin.GeneratePackagesListTask  hash ,expo.modules.plugin.GeneratePackagesListTask  invoke ,expo.modules.plugin.GeneratePackagesListTask  	namespace ,expo.modules.plugin.GeneratePackagesListTask  options ,expo.modules.plugin.GeneratePackagesListTask  
outputFile ,expo.modules.plugin.GeneratePackagesListTask  setGroup ,expo.modules.plugin.GeneratePackagesListTask  
setWorkingDir ,expo.modules.plugin.GeneratePackagesListTask  
workingDir ,expo.modules.plugin.GeneratePackagesListTask  ExpoAutolinkingConfig !expo.modules.plugin.configuration  
GradleProject !expo.modules.plugin.configuration  Publication !expo.modules.plugin.configuration  allProjects 7expo.modules.plugin.configuration.ExpoAutolinkingConfig  name /expo.modules.plugin.configuration.GradleProject  publication /expo.modules.plugin.configuration.GradleProject  usePublication /expo.modules.plugin.configuration.GradleProject  
artifactId -expo.modules.plugin.configuration.Publication  groupId -expo.modules.plugin.configuration.Publication  version -expo.modules.plugin.configuration.Publication  Colors expo.modules.plugin.text  	withColor expo.modules.plugin.text  GREEN expo.modules.plugin.text.Colors  YELLOW expo.modules.plugin.text.Colors  File java.io  absolutePath java.io.File  getABSOLUTEPath java.io.File  getAbsolutePath java.io.File  setAbsolutePath java.io.File  AndroidComponentsExtension 	java.lang  AutolinkigCommandBuilder 	java.lang  AutolinkingOptions 	java.lang  Class 	java.lang  Colors 	java.lang  ExpoGradleExtension 	java.lang  GeneratePackagesListTask 	java.lang  IllegalStateException 	java.lang  Integer 	java.lang  	KSPLookup 	java.lang  Paths 	java.lang  Task 	java.lang  VersionCatalogsExtension 	java.lang  	dependsOn 	java.lang  forEach 	java.lang  generatedFilesSrcDir 	java.lang  generatedPackageListFilename 	java.lang  generatedPackageListNamespace 	java.lang  	getOrNull 	java.lang  getValue 	java.lang  getVersionOrDefault 	java.lang  java 	java.lang  mapOf 	java.lang  	partition 	java.lang  replace 	java.lang  requireNotNull 	java.lang  
setIfNotExist 	java.lang  to 	java.lang  
trimIndent 	java.lang  with 	java.lang  	withColor 	java.lang  withSubprojects 	java.lang  parseInt java.lang.Integer  Paths 
java.nio.file  toString java.nio.file.Path  get java.nio.file.Paths  Optional 	java.util  getGETOrNull java.util.Optional  getGETVersionOrDefault java.util.Optional  getGetOrNull java.util.Optional  getGetVersionOrDefault java.util.Optional  	getOrNull java.util.Optional  getVersionOrDefault java.util.Optional  AndroidComponentsExtension kotlin  Any kotlin  AutolinkigCommandBuilder kotlin  AutolinkingOptions kotlin  Boolean kotlin  Char kotlin  Colors kotlin  ExpoGradleExtension kotlin  	Function0 kotlin  	Function1 kotlin  GeneratePackagesListTask kotlin  IllegalStateException kotlin  Int kotlin  Integer kotlin  	KSPLookup kotlin  Nothing kotlin  Pair kotlin  Paths kotlin  String kotlin  Task kotlin  Unit kotlin  VersionCatalogsExtension kotlin  	dependsOn kotlin  forEach kotlin  generatedFilesSrcDir kotlin  generatedPackageListFilename kotlin  generatedPackageListNamespace kotlin  	getOrNull kotlin  getValue kotlin  getVersionOrDefault kotlin  java kotlin  mapOf kotlin  	partition kotlin  replace kotlin  requireNotNull kotlin  
setIfNotExist kotlin  to kotlin  
trimIndent kotlin  with kotlin  	withColor kotlin  withSubprojects kotlin  getWITHColor 
kotlin.Any  getWithColor 
kotlin.Any  
component1 kotlin.Pair  
component2 kotlin.Pair  
getREPLACE 
kotlin.String  
getReplace 
kotlin.String  getTO 
kotlin.String  
getTRIMIndent 
kotlin.String  getTo 
kotlin.String  
getTrimIndent 
kotlin.String  getWITHColor 
kotlin.String  getWithColor 
kotlin.String  AndroidComponentsExtension kotlin.annotation  AutolinkigCommandBuilder kotlin.annotation  AutolinkingOptions kotlin.annotation  Colors kotlin.annotation  ExpoGradleExtension kotlin.annotation  GeneratePackagesListTask kotlin.annotation  IllegalStateException kotlin.annotation  Integer kotlin.annotation  	KSPLookup kotlin.annotation  Paths kotlin.annotation  Task kotlin.annotation  VersionCatalogsExtension kotlin.annotation  	dependsOn kotlin.annotation  forEach kotlin.annotation  generatedFilesSrcDir kotlin.annotation  generatedPackageListFilename kotlin.annotation  generatedPackageListNamespace kotlin.annotation  	getOrNull kotlin.annotation  getValue kotlin.annotation  getVersionOrDefault kotlin.annotation  java kotlin.annotation  mapOf kotlin.annotation  	partition kotlin.annotation  replace kotlin.annotation  requireNotNull kotlin.annotation  
setIfNotExist kotlin.annotation  to kotlin.annotation  
trimIndent kotlin.annotation  with kotlin.annotation  	withColor kotlin.annotation  withSubprojects kotlin.annotation  AndroidComponentsExtension kotlin.collections  AutolinkigCommandBuilder kotlin.collections  AutolinkingOptions kotlin.collections  Colors kotlin.collections  ExpoGradleExtension kotlin.collections  GeneratePackagesListTask kotlin.collections  IllegalStateException kotlin.collections  Integer kotlin.collections  	KSPLookup kotlin.collections  List kotlin.collections  Map kotlin.collections  Paths kotlin.collections  Task kotlin.collections  VersionCatalogsExtension kotlin.collections  	dependsOn kotlin.collections  forEach kotlin.collections  generatedFilesSrcDir kotlin.collections  generatedPackageListFilename kotlin.collections  generatedPackageListNamespace kotlin.collections  	getOrNull kotlin.collections  getValue kotlin.collections  getVersionOrDefault kotlin.collections  java kotlin.collections  mapOf kotlin.collections  	partition kotlin.collections  replace kotlin.collections  requireNotNull kotlin.collections  
setIfNotExist kotlin.collections  to kotlin.collections  
trimIndent kotlin.collections  with kotlin.collections  	withColor kotlin.collections  withSubprojects kotlin.collections  getPARTITION kotlin.collections.List  getPartition kotlin.collections.List  getGETValue kotlin.collections.Map  getGetValue kotlin.collections.Map  AndroidComponentsExtension kotlin.comparisons  AutolinkigCommandBuilder kotlin.comparisons  AutolinkingOptions kotlin.comparisons  Colors kotlin.comparisons  ExpoGradleExtension kotlin.comparisons  GeneratePackagesListTask kotlin.comparisons  IllegalStateException kotlin.comparisons  Integer kotlin.comparisons  	KSPLookup kotlin.comparisons  Paths kotlin.comparisons  Task kotlin.comparisons  VersionCatalogsExtension kotlin.comparisons  	dependsOn kotlin.comparisons  forEach kotlin.comparisons  generatedFilesSrcDir kotlin.comparisons  generatedPackageListFilename kotlin.comparisons  generatedPackageListNamespace kotlin.comparisons  	getOrNull kotlin.comparisons  getValue kotlin.comparisons  getVersionOrDefault kotlin.comparisons  java kotlin.comparisons  mapOf kotlin.comparisons  	partition kotlin.comparisons  replace kotlin.comparisons  requireNotNull kotlin.comparisons  
setIfNotExist kotlin.comparisons  to kotlin.comparisons  
trimIndent kotlin.comparisons  with kotlin.comparisons  	withColor kotlin.comparisons  withSubprojects kotlin.comparisons  AndroidComponentsExtension 	kotlin.io  AutolinkigCommandBuilder 	kotlin.io  AutolinkingOptions 	kotlin.io  Colors 	kotlin.io  ExpoGradleExtension 	kotlin.io  GeneratePackagesListTask 	kotlin.io  IllegalStateException 	kotlin.io  Integer 	kotlin.io  	KSPLookup 	kotlin.io  Paths 	kotlin.io  Task 	kotlin.io  VersionCatalogsExtension 	kotlin.io  	dependsOn 	kotlin.io  forEach 	kotlin.io  generatedFilesSrcDir 	kotlin.io  generatedPackageListFilename 	kotlin.io  generatedPackageListNamespace 	kotlin.io  	getOrNull 	kotlin.io  getValue 	kotlin.io  getVersionOrDefault 	kotlin.io  java 	kotlin.io  mapOf 	kotlin.io  	partition 	kotlin.io  replace 	kotlin.io  requireNotNull 	kotlin.io  
setIfNotExist 	kotlin.io  to 	kotlin.io  
trimIndent 	kotlin.io  with 	kotlin.io  	withColor 	kotlin.io  withSubprojects 	kotlin.io  AndroidComponentsExtension 
kotlin.jvm  AutolinkigCommandBuilder 
kotlin.jvm  AutolinkingOptions 
kotlin.jvm  Colors 
kotlin.jvm  ExpoGradleExtension 
kotlin.jvm  GeneratePackagesListTask 
kotlin.jvm  IllegalStateException 
kotlin.jvm  Integer 
kotlin.jvm  	KSPLookup 
kotlin.jvm  Paths 
kotlin.jvm  Task 
kotlin.jvm  VersionCatalogsExtension 
kotlin.jvm  	dependsOn 
kotlin.jvm  forEach 
kotlin.jvm  generatedFilesSrcDir 
kotlin.jvm  generatedPackageListFilename 
kotlin.jvm  generatedPackageListNamespace 
kotlin.jvm  	getOrNull 
kotlin.jvm  getValue 
kotlin.jvm  getVersionOrDefault 
kotlin.jvm  java 
kotlin.jvm  mapOf 
kotlin.jvm  	partition 
kotlin.jvm  replace 
kotlin.jvm  requireNotNull 
kotlin.jvm  
setIfNotExist 
kotlin.jvm  to 
kotlin.jvm  
trimIndent 
kotlin.jvm  with 
kotlin.jvm  	withColor 
kotlin.jvm  withSubprojects 
kotlin.jvm  	getOrNull kotlin.jvm.optionals  AndroidComponentsExtension 
kotlin.ranges  AutolinkigCommandBuilder 
kotlin.ranges  AutolinkingOptions 
kotlin.ranges  Colors 
kotlin.ranges  ExpoGradleExtension 
kotlin.ranges  GeneratePackagesListTask 
kotlin.ranges  IllegalStateException 
kotlin.ranges  Integer 
kotlin.ranges  	KSPLookup 
kotlin.ranges  Paths 
kotlin.ranges  Task 
kotlin.ranges  VersionCatalogsExtension 
kotlin.ranges  	dependsOn 
kotlin.ranges  forEach 
kotlin.ranges  generatedFilesSrcDir 
kotlin.ranges  generatedPackageListFilename 
kotlin.ranges  generatedPackageListNamespace 
kotlin.ranges  	getOrNull 
kotlin.ranges  getValue 
kotlin.ranges  getVersionOrDefault 
kotlin.ranges  java 
kotlin.ranges  mapOf 
kotlin.ranges  	partition 
kotlin.ranges  replace 
kotlin.ranges  requireNotNull 
kotlin.ranges  
setIfNotExist 
kotlin.ranges  to 
kotlin.ranges  
trimIndent 
kotlin.ranges  with 
kotlin.ranges  	withColor 
kotlin.ranges  withSubprojects 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  AndroidComponentsExtension kotlin.sequences  AutolinkigCommandBuilder kotlin.sequences  AutolinkingOptions kotlin.sequences  Colors kotlin.sequences  ExpoGradleExtension kotlin.sequences  GeneratePackagesListTask kotlin.sequences  IllegalStateException kotlin.sequences  Integer kotlin.sequences  	KSPLookup kotlin.sequences  Paths kotlin.sequences  Task kotlin.sequences  VersionCatalogsExtension kotlin.sequences  	dependsOn kotlin.sequences  forEach kotlin.sequences  generatedFilesSrcDir kotlin.sequences  generatedPackageListFilename kotlin.sequences  generatedPackageListNamespace kotlin.sequences  	getOrNull kotlin.sequences  getValue kotlin.sequences  getVersionOrDefault kotlin.sequences  java kotlin.sequences  mapOf kotlin.sequences  	partition kotlin.sequences  replace kotlin.sequences  requireNotNull kotlin.sequences  
setIfNotExist kotlin.sequences  to kotlin.sequences  
trimIndent kotlin.sequences  with kotlin.sequences  	withColor kotlin.sequences  withSubprojects kotlin.sequences  AndroidComponentsExtension kotlin.text  AutolinkigCommandBuilder kotlin.text  AutolinkingOptions kotlin.text  Colors kotlin.text  ExpoGradleExtension kotlin.text  GeneratePackagesListTask kotlin.text  IllegalStateException kotlin.text  Integer kotlin.text  	KSPLookup kotlin.text  Paths kotlin.text  Task kotlin.text  VersionCatalogsExtension kotlin.text  	dependsOn kotlin.text  forEach kotlin.text  generatedFilesSrcDir kotlin.text  generatedPackageListFilename kotlin.text  generatedPackageListNamespace kotlin.text  	getOrNull kotlin.text  getValue kotlin.text  getVersionOrDefault kotlin.text  java kotlin.text  mapOf kotlin.text  	partition kotlin.text  replace kotlin.text  requireNotNull kotlin.text  
setIfNotExist kotlin.text  to kotlin.text  
trimIndent kotlin.text  with kotlin.text  	withColor kotlin.text  withSubprojects kotlin.text  Plugin org.gradle.api  Project org.gradle.api  Task org.gradle.api  <SAM-CONSTRUCTOR> org.gradle.api.Action  AutolinkigCommandBuilder org.gradle.api.DefaultTask  AutolinkingOptions org.gradle.api.DefaultTask  Input org.gradle.api.DefaultTask  
OutputFile org.gradle.api.DefaultTask  Property org.gradle.api.DefaultTask  RegularFileProperty org.gradle.api.DefaultTask  String org.gradle.api.DefaultTask  commandLine org.gradle.api.DefaultTask  exec org.gradle.api.DefaultTask  invoke org.gradle.api.DefaultTask  	getByName )org.gradle.api.NamedDomainObjectContainer  Colors org.gradle.api.Project  Integer org.gradle.api.Project  	KSPLookup org.gradle.api.Project  defineDefaultProperties org.gradle.api.Project  dependencies org.gradle.api.Project  equals org.gradle.api.Project  evaluationDependsOn org.gradle.api.Project  
extensions org.gradle.api.Project  extra org.gradle.api.Project  findProject org.gradle.api.Project  getDEFINEDefaultProperties org.gradle.api.Project  getDEPENDENCIES org.gradle.api.Project  getDefineDefaultProperties org.gradle.api.Project  getDependencies org.gradle.api.Project  
getEXTENSIONS org.gradle.api.Project  getEXTRA org.gradle.api.Project  
getExtensions org.gradle.api.Project  getExtra org.gradle.api.Project  getGETValue org.gradle.api.Project  getGETVersionOrDefault org.gradle.api.Project  	getGRADLE org.gradle.api.Project  getGetValue org.gradle.api.Project  getGetVersionOrDefault org.gradle.api.Project  	getGradle org.gradle.api.Project  	getLAYOUT org.gradle.api.Project  	getLOGGER org.gradle.api.Project  	getLayout org.gradle.api.Project  	getLogger org.gradle.api.Project  getNAME org.gradle.api.Project  getName org.gradle.api.Project  getPATH org.gradle.api.Project  
getPROJECT org.gradle.api.Project  getPath org.gradle.api.Project  
getProject org.gradle.api.Project  
getROOTDir org.gradle.api.Project  
getRootDir org.gradle.api.Project  getSETIfNotExist org.gradle.api.Project  getSetIfNotExist org.gradle.api.Project  getTASKS org.gradle.api.Project  
getTRIMIndent org.gradle.api.Project  getTasks org.gradle.api.Project  
getTrimIndent org.gradle.api.Project  
getVERSION org.gradle.api.Project  getValue org.gradle.api.Project  
getVersion org.gradle.api.Project  getVersionOrDefault org.gradle.api.Project  getWITHColor org.gradle.api.Project  getWITHSubproject org.gradle.api.Project  getWITHSubprojects org.gradle.api.Project  getWithColor org.gradle.api.Project  getWithSubproject org.gradle.api.Project  getWithSubprojects org.gradle.api.Project  gradle org.gradle.api.Project  layout org.gradle.api.Project  logger org.gradle.api.Project  name org.gradle.api.Project  path org.gradle.api.Project  project org.gradle.api.Project  rootDir org.gradle.api.Project  setDependencies org.gradle.api.Project  
setExtensions org.gradle.api.Project  	setGradle org.gradle.api.Project  
setIfNotExist org.gradle.api.Project  	setLayout org.gradle.api.Project  	setLogger org.gradle.api.Project  setName org.gradle.api.Project  setPath org.gradle.api.Project  
setProject org.gradle.api.Project  
setRootDir org.gradle.api.Project  setTasks org.gradle.api.Project  
setVersion org.gradle.api.Project  tasks org.gradle.api.Project  
trimIndent org.gradle.api.Project  version org.gradle.api.Project  	withColor org.gradle.api.Project  withSubproject org.gradle.api.Project  withSubprojects org.gradle.api.Project  
Dependency org.gradle.api.artifacts  VersionCatalog org.gradle.api.artifacts  VersionCatalogsExtension org.gradle.api.artifacts  findVersion 'org.gradle.api.artifacts.VersionCatalog  find 1org.gradle.api.artifacts.VersionCatalogsExtension  getREQUIREDVersion *org.gradle.api.artifacts.VersionConstraint  getRequiredVersion *org.gradle.api.artifacts.VersionConstraint  requiredVersion *org.gradle.api.artifacts.VersionConstraint  setRequiredVersion *org.gradle.api.artifacts.VersionConstraint  add .org.gradle.api.artifacts.dsl.DependencyHandler  	Directory org.gradle.api.file  RegularFile org.gradle.api.file  RegularFileProperty org.gradle.api.file  dir %org.gradle.api.file.DirectoryProperty  file %org.gradle.api.file.DirectoryProperty  buildDirectory !org.gradle.api.file.ProjectLayout  getBUILDDirectory !org.gradle.api.file.ProjectLayout  getBuildDirectory !org.gradle.api.file.ProjectLayout  setBuildDirectory !org.gradle.api.file.ProjectLayout  asFile org.gradle.api.file.RegularFile  	getASFile org.gradle.api.file.RegularFile  	getAsFile org.gradle.api.file.RegularFile  	setAsFile org.gradle.api.file.RegularFile  get 'org.gradle.api.file.RegularFileProperty  set 'org.gradle.api.file.RegularFileProperty  AutolinkigCommandBuilder $org.gradle.api.internal.AbstractTask  AutolinkingOptions $org.gradle.api.internal.AbstractTask  Input $org.gradle.api.internal.AbstractTask  
OutputFile $org.gradle.api.internal.AbstractTask  Property $org.gradle.api.internal.AbstractTask  RegularFileProperty $org.gradle.api.internal.AbstractTask  String $org.gradle.api.internal.AbstractTask  commandLine $org.gradle.api.internal.AbstractTask  exec $org.gradle.api.internal.AbstractTask  invoke $org.gradle.api.internal.AbstractTask  AutolinkigCommandBuilder &org.gradle.api.internal.ConventionTask  AutolinkingOptions &org.gradle.api.internal.ConventionTask  Input &org.gradle.api.internal.ConventionTask  
OutputFile &org.gradle.api.internal.ConventionTask  Property &org.gradle.api.internal.ConventionTask  RegularFileProperty &org.gradle.api.internal.ConventionTask  String &org.gradle.api.internal.ConventionTask  commandLine &org.gradle.api.internal.ConventionTask  exec &org.gradle.api.internal.ConventionTask  invoke &org.gradle.api.internal.ConventionTask  
extensions  org.gradle.api.invocation.Gradle  
getEXTENSIONS  org.gradle.api.invocation.Gradle  
getExtensions  org.gradle.api.invocation.Gradle  
setExtensions  org.gradle.api.invocation.Gradle  Logger org.gradle.api.logging  quiet org.gradle.api.logging.Logger  warn org.gradle.api.logging.Logger  ExtraPropertiesExtension org.gradle.api.plugins  
findByType )org.gradle.api.plugins.ExtensionContainer  	getByType )org.gradle.api.plugins.ExtensionContainer  get /org.gradle.api.plugins.ExtraPropertiesExtension  getSETIfNotExist /org.gradle.api.plugins.ExtraPropertiesExtension  getSetIfNotExist /org.gradle.api.plugins.ExtraPropertiesExtension  has /org.gradle.api.plugins.ExtraPropertiesExtension  set /org.gradle.api.plugins.ExtraPropertiesExtension  
setIfNotExist /org.gradle.api.plugins.ExtraPropertiesExtension  Property org.gradle.api.provider  Provider org.gradle.api.provider  get  org.gradle.api.provider.Property  set  org.gradle.api.provider.Property  Exec org.gradle.api.tasks  Input org.gradle.api.tasks  
OutputFile org.gradle.api.tasks  TaskProvider org.gradle.api.tasks  AutolinkigCommandBuilder %org.gradle.api.tasks.AbstractExecTask  AutolinkingOptions %org.gradle.api.tasks.AbstractExecTask  Input %org.gradle.api.tasks.AbstractExecTask  
OutputFile %org.gradle.api.tasks.AbstractExecTask  Property %org.gradle.api.tasks.AbstractExecTask  RegularFileProperty %org.gradle.api.tasks.AbstractExecTask  String %org.gradle.api.tasks.AbstractExecTask  commandLine %org.gradle.api.tasks.AbstractExecTask  exec %org.gradle.api.tasks.AbstractExecTask  invoke %org.gradle.api.tasks.AbstractExecTask  AutolinkigCommandBuilder org.gradle.api.tasks.Exec  AutolinkingOptions org.gradle.api.tasks.Exec  Input org.gradle.api.tasks.Exec  
OutputFile org.gradle.api.tasks.Exec  Property org.gradle.api.tasks.Exec  RegularFileProperty org.gradle.api.tasks.Exec  String org.gradle.api.tasks.Exec  commandLine org.gradle.api.tasks.Exec  exec org.gradle.api.tasks.Exec  invoke org.gradle.api.tasks.Exec  named "org.gradle.api.tasks.TaskContainer  register "org.gradle.api.tasks.TaskContainer  	dependsOn !org.gradle.api.tasks.TaskProvider  getDEPENDSOn !org.gradle.api.tasks.TaskProvider  getDependsOn !org.gradle.api.tasks.TaskProvider  extra #org.gradle.internal.extensions.core                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     