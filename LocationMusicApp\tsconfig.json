{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "bundler", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "allowJs": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/screens/*": ["src/screens/*"], "@/services/*": ["src/services/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/hooks/*": ["src/hooks/*"], "@/store/*": ["src/store/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules"]}