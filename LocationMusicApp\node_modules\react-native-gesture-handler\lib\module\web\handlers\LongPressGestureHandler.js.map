{"version": 3, "sources": ["LongPressGestureHandler.ts"], "names": ["State", "Gesture<PERSON>andler", "DEFAULT_MIN_DURATION_MS", "DEFAULT_MAX_DIST_DP", "SCALING_FACTOR", "LongPressGestureHandler", "defaultMaxDistSq", "init", "ref", "propsRef", "config", "enableContextMenu", "undefined", "transformNativeEvent", "duration", "Date", "now", "startTime", "updateGestureConfig", "enabled", "props", "minDurationMs", "maxDist", "maxDistSq", "numberOfPointers", "resetConfig", "onStateChange", "_newState", "_oldState", "clearTimeout", "activationTimeout", "onPointerDown", "event", "isButtonInConfig", "button", "tracker", "addToTracker", "startX", "x", "startY", "y", "tryBegin", "tryActivate", "tryToSendTouchEvent", "onPointerAdd", "trackedPointersCount", "fail", "absoluteCoordsAverage", "getAbsoluteCoordsAverage", "onPointerMove", "track", "checkDistanceFail", "onPointerOutOfBounds", "onPointerUp", "removeFromTracker", "pointerId", "state", "ACTIVE", "end", "onPointerRemove", "UNDETERMINED", "previousTime", "begin", "setTimeout", "activate", "dx", "dy", "distSq", "cancel"], "mappings": ";;AAAA,SAASA,KAAT,QAAsB,aAAtB;AAGA,OAAOC,cAAP,MAA2B,kBAA3B;AAEA,MAAMC,uBAAuB,GAAG,GAAhC;AACA,MAAMC,mBAAmB,GAAG,EAA5B;AACA,MAAMC,cAAc,GAAG,EAAvB;AAEA,eAAe,MAAMC,uBAAN,SAAsCJ,cAAtC,CAAqD;AAAA;AAAA;;AAAA,2CAC1CC,uBAD0C;;AAAA,8CAEvCC,mBAAmB,GAAGC,cAFiB;;AAAA,uCAI9C,KAAKE,gBAJyC;;AAAA,8CAKvC,CALuC;;AAAA,oCAMjD,CANiD;;AAAA,oCAOjD,CAPiD;;AAAA,uCAS9C,CAT8C;;AAAA,0CAU3C,CAV2C;;AAAA;AAAA;;AAc3DC,EAAAA,IAAI,CAACC,GAAD,EAAcC,QAAd,EAAkD;AAC3D,QAAI,KAAKC,MAAL,CAAYC,iBAAZ,KAAkCC,SAAtC,EAAiD;AAC/C,WAAKF,MAAL,CAAYC,iBAAZ,GAAgC,KAAhC;AACD;;AAED,UAAMJ,IAAN,CAAWC,GAAX,EAAgBC,QAAhB;AACD;;AAESI,EAAAA,oBAAoB,GAAG;AAC/B,WAAO,EACL,GAAG,MAAMA,oBAAN,EADE;AAELC,MAAAA,QAAQ,EAAEC,IAAI,CAACC,GAAL,KAAa,KAAKC;AAFvB,KAAP;AAID;;AAEMC,EAAAA,mBAAmB,CAAC;AAAEC,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAGC;AAArB,GAAD,EAA6C;AACrE,UAAMF,mBAAN,CAA0B;AAAEC,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAGC;AAAvB,KAA1B;;AAEA,QAAI,KAAKV,MAAL,CAAYW,aAAZ,KAA8BT,SAAlC,EAA6C;AAC3C,WAAKS,aAAL,GAAqB,KAAKX,MAAL,CAAYW,aAAjC;AACD;;AAED,QAAI,KAAKX,MAAL,CAAYY,OAAZ,KAAwBV,SAA5B,EAAuC;AACrC,WAAKW,SAAL,GAAiB,KAAKb,MAAL,CAAYY,OAAZ,GAAsB,KAAKZ,MAAL,CAAYY,OAAnD;AACD;;AAED,QAAI,KAAKZ,MAAL,CAAYc,gBAAZ,KAAiCZ,SAArC,EAAgD;AAC9C,WAAKY,gBAAL,GAAwB,KAAKd,MAAL,CAAYc,gBAApC;AACD;AACF;;AAESC,EAAAA,WAAW,GAAS;AAC5B,UAAMA,WAAN;AACA,SAAKJ,aAAL,GAAqBnB,uBAArB;AACA,SAAKqB,SAAL,GAAiB,KAAKjB,gBAAtB;AACD;;AAESoB,EAAAA,aAAa,CAACC,SAAD,EAAmBC,SAAnB,EAA2C;AAChEC,IAAAA,YAAY,CAAC,KAAKC,iBAAN,CAAZ;AACD;;AAESC,EAAAA,aAAa,CAACC,KAAD,EAA4B;AACjD,QAAI,CAAC,KAAKC,gBAAL,CAAsBD,KAAK,CAACE,MAA5B,CAAL,EAA0C;AACxC;AACD;;AAED,SAAKC,OAAL,CAAaC,YAAb,CAA0BJ,KAA1B;AACA,UAAMD,aAAN,CAAoBC,KAApB;AAEA,SAAKK,MAAL,GAAcL,KAAK,CAACM,CAApB;AACA,SAAKC,MAAL,GAAcP,KAAK,CAACQ,CAApB;AAEA,SAAKC,QAAL;AACA,SAAKC,WAAL;AAEA,SAAKC,mBAAL,CAAyBX,KAAzB;AACD;;AACSY,EAAAA,YAAY,CAACZ,KAAD,EAA4B;AAChD,UAAMY,YAAN,CAAmBZ,KAAnB;AACA,SAAKG,OAAL,CAAaC,YAAb,CAA0BJ,KAA1B;;AAEA,QAAI,KAAKG,OAAL,CAAaU,oBAAb,GAAoC,KAAKrB,gBAA7C,EAA+D;AAC7D,WAAKsB,IAAL;AACA;AACD;;AAED,UAAMC,qBAAqB,GAAG,KAAKZ,OAAL,CAAaa,wBAAb,EAA9B;AAEA,SAAKX,MAAL,GAAcU,qBAAqB,CAACT,CAApC;AACA,SAAKC,MAAL,GAAcQ,qBAAqB,CAACP,CAApC;AAEA,SAAKE,WAAL;AACD;;AAESO,EAAAA,aAAa,CAACjB,KAAD,EAA4B;AACjD,UAAMiB,aAAN,CAAoBjB,KAApB;AACA,SAAKG,OAAL,CAAae,KAAb,CAAmBlB,KAAnB;AACA,SAAKmB,iBAAL;AACD;;AAESC,EAAAA,oBAAoB,CAACpB,KAAD,EAA4B;AACxD,UAAMoB,oBAAN,CAA2BpB,KAA3B;AACA,SAAKG,OAAL,CAAae,KAAb,CAAmBlB,KAAnB;AACA,SAAKmB,iBAAL;AACD;;AAESE,EAAAA,WAAW,CAACrB,KAAD,EAA4B;AAC/C,UAAMqB,WAAN,CAAkBrB,KAAlB;AACA,SAAKG,OAAL,CAAamB,iBAAb,CAA+BtB,KAAK,CAACuB,SAArC;;AAEA,QAAI,KAAKC,KAAL,KAAexD,KAAK,CAACyD,MAAzB,EAAiC;AAC/B,WAAKC,GAAL;AACD,KAFD,MAEO;AACL,WAAKZ,IAAL;AACD;AACF;;AAESa,EAAAA,eAAe,CAAC3B,KAAD,EAA4B;AACnD,UAAM2B,eAAN,CAAsB3B,KAAtB;AACA,SAAKG,OAAL,CAAamB,iBAAb,CAA+BtB,KAAK,CAACuB,SAArC;;AAEA,QACE,KAAKpB,OAAL,CAAaU,oBAAb,GAAoC,KAAKrB,gBAAzC,IACA,KAAKgC,KAAL,KAAexD,KAAK,CAACyD,MAFvB,EAGE;AACA,WAAKX,IAAL;AACD;AACF;;AAEOL,EAAAA,QAAQ,GAAS;AACvB,QAAI,KAAKe,KAAL,KAAexD,KAAK,CAAC4D,YAAzB,EAAuC;AACrC;AACD;;AAED,SAAKC,YAAL,GAAoB9C,IAAI,CAACC,GAAL,EAApB;AACA,SAAKC,SAAL,GAAiB,KAAK4C,YAAtB;AAEA,SAAKC,KAAL;AACD;;AAEOpB,EAAAA,WAAW,GAAS;AAC1B,QAAI,KAAKP,OAAL,CAAaU,oBAAb,KAAsC,KAAKrB,gBAA/C,EAAiE;AAC/D;AACD;;AAED,QAAI,KAAKH,aAAL,GAAqB,CAAzB,EAA4B;AAC1B,WAAKS,iBAAL,GAAyBiC,UAAU,CAAC,MAAM;AACxC,aAAKC,QAAL;AACD,OAFkC,EAEhC,KAAK3C,aAF2B,CAAnC;AAGD,KAJD,MAIO,IAAI,KAAKA,aAAL,KAAuB,CAA3B,EAA8B;AACnC,WAAK2C,QAAL;AACD;AACF;;AAEOb,EAAAA,iBAAiB,GAAS;AAChC,UAAMJ,qBAAqB,GAAG,KAAKZ,OAAL,CAAaa,wBAAb,EAA9B;AAEA,UAAMiB,EAAE,GAAGlB,qBAAqB,CAACT,CAAtB,GAA0B,KAAKD,MAA1C;AACA,UAAM6B,EAAE,GAAGnB,qBAAqB,CAACP,CAAtB,GAA0B,KAAKD,MAA1C;AACA,UAAM4B,MAAM,GAAGF,EAAE,GAAGA,EAAL,GAAUC,EAAE,GAAGA,EAA9B;;AAEA,QAAIC,MAAM,IAAI,KAAK5C,SAAnB,EAA8B;AAC5B;AACD;;AAED,QAAI,KAAKiC,KAAL,KAAexD,KAAK,CAACyD,MAAzB,EAAiC;AAC/B,WAAKW,MAAL;AACD,KAFD,MAEO;AACL,WAAKtB,IAAL;AACD;AACF;;AApKiE", "sourcesContent": ["import { State } from '../../State';\nimport { AdaptedEvent, Config } from '../interfaces';\n\nimport GestureHandler from './GestureHandler';\n\nconst DEFAULT_MIN_DURATION_MS = 500;\nconst DEFAULT_MAX_DIST_DP = 10;\nconst SCALING_FACTOR = 10;\n\nexport default class LongPressGestureHandler extends GestureHandler {\n  private minDurationMs = DEFAULT_MIN_DURATION_MS;\n  private defaultMaxDistSq = DEFAULT_MAX_DIST_DP * SCALING_FACTOR;\n\n  private maxDistSq = this.defaultMaxDistSq;\n  private numberOfPointers = 1;\n  private startX = 0;\n  private startY = 0;\n\n  private startTime = 0;\n  private previousTime = 0;\n\n  private activationTimeout: number | undefined;\n\n  public init(ref: number, propsRef: React.RefObject<unknown>) {\n    if (this.config.enableContextMenu === undefined) {\n      this.config.enableContextMenu = false;\n    }\n\n    super.init(ref, propsRef);\n  }\n\n  protected transformNativeEvent() {\n    return {\n      ...super.transformNativeEvent(),\n      duration: Date.now() - this.startTime,\n    };\n  }\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    super.updateGestureConfig({ enabled: enabled, ...props });\n\n    if (this.config.minDurationMs !== undefined) {\n      this.minDurationMs = this.config.minDurationMs;\n    }\n\n    if (this.config.maxDist !== undefined) {\n      this.maxDistSq = this.config.maxDist * this.config.maxDist;\n    }\n\n    if (this.config.numberOfPointers !== undefined) {\n      this.numberOfPointers = this.config.numberOfPointers;\n    }\n  }\n\n  protected resetConfig(): void {\n    super.resetConfig();\n    this.minDurationMs = DEFAULT_MIN_DURATION_MS;\n    this.maxDistSq = this.defaultMaxDistSq;\n  }\n\n  protected onStateChange(_newState: State, _oldState: State): void {\n    clearTimeout(this.activationTimeout);\n  }\n\n  protected onPointerDown(event: AdaptedEvent): void {\n    if (!this.isButtonInConfig(event.button)) {\n      return;\n    }\n\n    this.tracker.addToTracker(event);\n    super.onPointerDown(event);\n\n    this.startX = event.x;\n    this.startY = event.y;\n\n    this.tryBegin();\n    this.tryActivate();\n\n    this.tryToSendTouchEvent(event);\n  }\n  protected onPointerAdd(event: AdaptedEvent): void {\n    super.onPointerAdd(event);\n    this.tracker.addToTracker(event);\n\n    if (this.tracker.trackedPointersCount > this.numberOfPointers) {\n      this.fail();\n      return;\n    }\n\n    const absoluteCoordsAverage = this.tracker.getAbsoluteCoordsAverage();\n\n    this.startX = absoluteCoordsAverage.x;\n    this.startY = absoluteCoordsAverage.y;\n\n    this.tryActivate();\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    super.onPointerMove(event);\n    this.tracker.track(event);\n    this.checkDistanceFail();\n  }\n\n  protected onPointerOutOfBounds(event: AdaptedEvent): void {\n    super.onPointerOutOfBounds(event);\n    this.tracker.track(event);\n    this.checkDistanceFail();\n  }\n\n  protected onPointerUp(event: AdaptedEvent): void {\n    super.onPointerUp(event);\n    this.tracker.removeFromTracker(event.pointerId);\n\n    if (this.state === State.ACTIVE) {\n      this.end();\n    } else {\n      this.fail();\n    }\n  }\n\n  protected onPointerRemove(event: AdaptedEvent): void {\n    super.onPointerRemove(event);\n    this.tracker.removeFromTracker(event.pointerId);\n\n    if (\n      this.tracker.trackedPointersCount < this.numberOfPointers &&\n      this.state !== State.ACTIVE\n    ) {\n      this.fail();\n    }\n  }\n\n  private tryBegin(): void {\n    if (this.state !== State.UNDETERMINED) {\n      return;\n    }\n\n    this.previousTime = Date.now();\n    this.startTime = this.previousTime;\n\n    this.begin();\n  }\n\n  private tryActivate(): void {\n    if (this.tracker.trackedPointersCount !== this.numberOfPointers) {\n      return;\n    }\n\n    if (this.minDurationMs > 0) {\n      this.activationTimeout = setTimeout(() => {\n        this.activate();\n      }, this.minDurationMs);\n    } else if (this.minDurationMs === 0) {\n      this.activate();\n    }\n  }\n\n  private checkDistanceFail(): void {\n    const absoluteCoordsAverage = this.tracker.getAbsoluteCoordsAverage();\n\n    const dx = absoluteCoordsAverage.x - this.startX;\n    const dy = absoluteCoordsAverage.y - this.startY;\n    const distSq = dx * dx + dy * dy;\n\n    if (distSq <= this.maxDistSq) {\n      return;\n    }\n\n    if (this.state === State.ACTIVE) {\n      this.cancel();\n    } else {\n      this.fail();\n    }\n  }\n}\n"]}