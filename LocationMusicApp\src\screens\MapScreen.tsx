import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import MapView, { <PERSON><PERSON>, Circle, PROVIDER_GOOGLE } from 'react-native-maps';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useApp } from '../store/AppContext';
import { RootStackParamList } from '../navigation/AppNavigator';
import { LocationTag, TagCategory, Location } from '../types';

type MapScreenNavigationProp = StackNavigationProp<RootStackParamList>;

interface MapMarkerData {
  tag: LocationTag;
  coordinate: {
    latitude: number;
    longitude: number;
  };
}

export default function MapScreen() {
  const navigation = useNavigation<MapScreenNavigationProp>();
  const { state, actions } = useApp();
  const mapRef = useRef<MapView>(null);
  
  const [selectedTag, setSelectedTag] = useState<LocationTag | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    showUserTags: true,
    showPublicTags: true,
    categories: Object.values(TagCategory),
  });

  useEffect(() => {
    // Load tags when component mounts
    loadTags();
  }, []);

  useEffect(() => {
    // Center map on current location when available
    if (state.currentLocation && mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: state.currentLocation.latitude,
        longitude: state.currentLocation.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      }, 1000);
    }
  }, [state.currentLocation]);

  const loadTags = async () => {
    try {
      await actions.loadUserTags();
      await actions.loadPublicTags();
    } catch (error) {
      Alert.alert('Error', 'Failed to load location tags');
    }
  };

  const getFilteredTags = (): LocationTag[] => {
    let tags: LocationTag[] = [];
    
    if (filters.showUserTags) {
      tags = [...tags, ...state.userTags];
    }
    
    if (filters.showPublicTags) {
      tags = [...tags, ...state.publicTags];
    }
    
    return tags.filter(tag => filters.categories.includes(tag.category));
  };

  const getMarkerColor = (category: TagCategory): string => {
    const colorMap = {
      [TagCategory.DRIVING]: '#FF5722',
      [TagCategory.WORKOUT]: '#4CAF50',
      [TagCategory.RELAXATION]: '#2196F3',
      [TagCategory.STUDY]: '#9C27B0',
      [TagCategory.PARTY]: '#FF9800',
      [TagCategory.CUSTOM]: '#607D8B',
    };
    return colorMap[category] || '#607D8B';
  };

  const handleMarkerPress = (tag: LocationTag) => {
    setSelectedTag(tag);
  };

  const handleTagPlay = async (tag: LocationTag) => {
    if (tag.tracks.length > 0) {
      try {
        await actions.playTrack(tag.tracks[0]);
        Alert.alert('Playing', `Started playing music from "${tag.name}"`);
      } catch (error) {
        Alert.alert('Error', 'Failed to play music');
      }
    } else if (tag.playlists.length > 0 && tag.playlists[0].tracks.length > 0) {
      try {
        await actions.playTrack(tag.playlists[0].tracks[0]);
        Alert.alert('Playing', `Started playing playlist from "${tag.name}"`);
      } catch (error) {
        Alert.alert('Error', 'Failed to play music');
      }
    } else {
      Alert.alert('No Music', 'This tag has no associated music');
    }
  };

  const handleCurrentLocationPress = async () => {
    try {
      const location = await actions.getCurrentLocation();
      if (location && mapRef.current) {
        mapRef.current.animateToRegion({
          latitude: location.latitude,
          longitude: location.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }, 1000);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to get current location');
    }
  };

  const toggleCategoryFilter = (category: TagCategory) => {
    setFilters(prev => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter(c => c !== category)
        : [...prev.categories, category],
    }));
  };

  const filteredTags = getFilteredTags();

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.mapContainer}>
        <MapView
          ref={mapRef}
          style={styles.map}
          provider={PROVIDER_GOOGLE}
          showsUserLocation={true}
          showsMyLocationButton={false}
          initialRegion={{
            latitude: state.currentLocation?.latitude || 37.7749,
            longitude: state.currentLocation?.longitude || -122.4194,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
          }}
        >
          {/* Render location tags as markers */}
          {filteredTags.map((tag) => (
            <React.Fragment key={tag.id}>
              <Marker
                coordinate={{
                  latitude: tag.location.latitude,
                  longitude: tag.location.longitude,
                }}
                title={tag.name}
                description={tag.description}
                pinColor={getMarkerColor(tag.category)}
                onPress={() => handleMarkerPress(tag)}
              />
              
              {/* Render radius circle for radius-based tags */}
              {tag.radius && (
                <Circle
                  center={{
                    latitude: tag.location.latitude,
                    longitude: tag.location.longitude,
                  }}
                  radius={tag.radius}
                  strokeColor={getMarkerColor(tag.category)}
                  strokeWidth={2}
                  fillColor={`${getMarkerColor(tag.category)}20`}
                />
              )}
            </React.Fragment>
          ))}
        </MapView>

        {/* Map Controls */}
        <View style={styles.mapControls}>
          <TouchableOpacity
            style={styles.controlButton}
            onPress={handleCurrentLocationPress}
          >
            <Ionicons name="locate" size={24} color="#007AFF" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.controlButton}
            onPress={() => setShowFilters(true)}
          >
            <Ionicons name="filter" size={24} color="#007AFF" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.controlButton}
            onPress={() => navigation.navigate('CreateTag')}
          >
            <Ionicons name="add" size={24} color="#007AFF" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Tag Detail Modal */}
      <Modal
        visible={selectedTag !== null}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setSelectedTag(null)}
      >
        {selectedTag && (
          <SafeAreaView style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setSelectedTag(null)}
              >
                <Ionicons name="close" size={24} color="#007AFF" />
              </TouchableOpacity>
              <Text style={styles.modalTitle}>{selectedTag.name}</Text>
              <TouchableOpacity
                style={styles.modalActionButton}
                onPress={() => {
                  setSelectedTag(null);
                  navigation.navigate('TagDetail', { tagId: selectedTag.id });
                }}
              >
                <Ionicons name="information-circle" size={24} color="#007AFF" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              <View style={styles.tagInfo}>
                <Text style={styles.tagCategory}>{selectedTag.category.replace('_', ' ').toUpperCase()}</Text>
                {selectedTag.description && (
                  <Text style={styles.tagDescription}>{selectedTag.description}</Text>
                )}
                
                <View style={styles.tagStats}>
                  <View style={styles.statItem}>
                    <Ionicons name="musical-notes" size={16} color="#666" />
                    <Text style={styles.statText}>
                      {selectedTag.tracks.length} tracks, {selectedTag.playlists.length} playlists
                    </Text>
                  </View>
                  
                  <View style={styles.statItem}>
                    <Ionicons name="location" size={16} color="#666" />
                    <Text style={styles.statText}>
                      {selectedTag.type === 'radius' 
                        ? `${selectedTag.radius}m radius` 
                        : 'Route segment'
                      }
                    </Text>
                  </View>
                  
                  <View style={styles.statItem}>
                    <Ionicons name="time" size={16} color="#666" />
                    <Text style={styles.statText}>
                      Created {new Date(selectedTag.createdAt).toLocaleDateString()}
                    </Text>
                  </View>
                </View>

                {/* Music Preview */}
                {selectedTag.tracks.length > 0 && (
                  <View style={styles.musicSection}>
                    <Text style={styles.sectionTitle}>Tracks</Text>
                    {selectedTag.tracks.slice(0, 3).map((track, index) => (
                      <View key={track.id} style={styles.trackItem}>
                        <Text style={styles.trackTitle}>{track.title}</Text>
                        <Text style={styles.trackArtist}>{track.artist}</Text>
                      </View>
                    ))}
                    {selectedTag.tracks.length > 3 && (
                      <Text style={styles.moreText}>
                        +{selectedTag.tracks.length - 3} more tracks
                      </Text>
                    )}
                  </View>
                )}

                {/* Action Buttons */}
                <View style={styles.actionButtons}>
                  <TouchableOpacity
                    style={[styles.actionButton, styles.playButton]}
                    onPress={() => handleTagPlay(selectedTag)}
                  >
                    <Ionicons name="play" size={20} color="white" />
                    <Text style={styles.actionButtonText}>Play</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[styles.actionButton, styles.detailButton]}
                    onPress={() => {
                      setSelectedTag(null);
                      navigation.navigate('TagDetail', { tagId: selectedTag.id });
                    }}
                  >
                    <Ionicons name="information-circle" size={20} color="#007AFF" />
                    <Text style={[styles.actionButtonText, { color: '#007AFF' }]}>Details</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </ScrollView>
          </SafeAreaView>
        )}
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  mapControls: {
    position: 'absolute',
    right: 16,
    top: 50,
    flexDirection: 'column',
  },
  controlButton: {
    backgroundColor: 'white',
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalCloseButton: {
    padding: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  modalActionButton: {
    padding: 8,
  },
  modalContent: {
    flex: 1,
  },
  tagInfo: {
    padding: 20,
  },
  tagCategory: {
    fontSize: 12,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 8,
    letterSpacing: 1,
  },
  tagDescription: {
    fontSize: 16,
    color: '#333',
    marginBottom: 16,
    lineHeight: 22,
  },
  tagStats: {
    marginBottom: 20,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  musicSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  trackItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  trackTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  trackArtist: {
    fontSize: 12,
    color: '#666',
  },
  moreText: {
    fontSize: 12,
    color: '#007AFF',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    flex: 0.48,
  },
  playButton: {
    backgroundColor: '#4CAF50',
  },
  detailButton: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
    color: 'white',
  },
  filterSection: {
    padding: 20,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'white',
    borderRadius: 8,
    marginBottom: 8,
  },
  filterOptionText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 12,
  },
});
