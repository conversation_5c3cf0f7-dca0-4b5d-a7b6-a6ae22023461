import * as ExpoLocation from 'expo-location';
import * as TaskManager from 'expo-task-manager';
import * as BackgroundFetch from 'expo-background-fetch';
import { Location, LocationAccuracy, AppError, ErrorCode, LocationTag } from '../types';

const LOCATION_TASK_NAME = 'background-location-task';
const GEOFENCE_TASK_NAME = 'geofence-task';

export class LocationService {
  private static instance: LocationService;
  private currentLocation: Location | null = null;
  private isTracking: boolean = false;
  private locationSubscription: ExpoLocation.LocationSubscription | null = null;
  private geofences: Map<string, LocationTag> = new Map();
  private locationUpdateCallbacks: ((location: Location) => void)[] = [];
  private geofenceCallbacks: ((tagId: string, event: 'enter' | 'exit') => void)[] = [];

  private constructor() {
    this.setupBackgroundTasks();
  }

  public static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  // Permission Management
  public async requestPermissions(): Promise<boolean> {
    try {
      // Request foreground location permission
      const { status: foregroundStatus } = await ExpoLocation.requestForegroundPermissionsAsync();
      
      if (foregroundStatus !== 'granted') {
        throw new Error('Foreground location permission denied');
      }

      // Request background location permission
      const { status: backgroundStatus } = await ExpoLocation.requestBackgroundPermissionsAsync();
      
      if (backgroundStatus !== 'granted') {
        console.warn('Background location permission denied - some features may be limited');
      }

      return true;
    } catch (error) {
      console.error('Location permission error:', error);
      throw {
        code: ErrorCode.LOCATION_PERMISSION_DENIED,
        message: 'Location permissions are required for this app to function',
        details: error,
        timestamp: Date.now(),
      } as AppError;
    }
  }

  public async checkPermissions(): Promise<{
    foreground: boolean;
    background: boolean;
  }> {
    const foregroundStatus = await ExpoLocation.getForegroundPermissionsAsync();
    const backgroundStatus = await ExpoLocation.getBackgroundPermissionsAsync();

    return {
      foreground: foregroundStatus.status === 'granted',
      background: backgroundStatus.status === 'granted',
    };
  }

  // Location Tracking
  public async startLocationTracking(accuracy: LocationAccuracy = LocationAccuracy.BALANCED): Promise<void> {
    try {
      const hasPermissions = await this.requestPermissions();
      if (!hasPermissions) {
        throw new Error('Location permissions not granted');
      }

      const locationOptions = this.getLocationOptions(accuracy);

      // Start foreground location tracking
      this.locationSubscription = await ExpoLocation.watchPositionAsync(
        locationOptions,
        (locationData) => {
          const location: Location = {
            latitude: locationData.coords.latitude,
            longitude: locationData.coords.longitude,
            accuracy: locationData.coords.accuracy || undefined,
            altitude: locationData.coords.altitude || undefined,
            heading: locationData.coords.heading || undefined,
            speed: locationData.coords.speed || undefined,
            timestamp: locationData.timestamp,
          };

          this.currentLocation = location;
          this.notifyLocationUpdate(location);
          this.checkGeofences(location);
        }
      );

      // Start background location tracking
      await this.startBackgroundLocationTask();

      this.isTracking = true;
      console.log('Location tracking started');
    } catch (error) {
      console.error('Failed to start location tracking:', error);
      throw {
        code: ErrorCode.LOCATION_UNAVAILABLE,
        message: 'Failed to start location tracking',
        details: error,
        timestamp: Date.now(),
      } as AppError;
    }
  }

  public async stopLocationTracking(): Promise<void> {
    try {
      if (this.locationSubscription) {
        this.locationSubscription.remove();
        this.locationSubscription = null;
      }

      await TaskManager.unregisterTaskAsync(LOCATION_TASK_NAME);
      await TaskManager.unregisterTaskAsync(GEOFENCE_TASK_NAME);

      this.isTracking = false;
      console.log('Location tracking stopped');
    } catch (error) {
      console.error('Failed to stop location tracking:', error);
    }
  }

  public getCurrentLocation(): Location | null {
    return this.currentLocation;
  }

  public async getOneTimeLocation(accuracy: LocationAccuracy = LocationAccuracy.BALANCED): Promise<Location> {
    try {
      const hasPermissions = await this.requestPermissions();
      if (!hasPermissions) {
        throw new Error('Location permissions not granted');
      }

      const locationOptions = this.getLocationOptions(accuracy);
      const locationData = await ExpoLocation.getCurrentPositionAsync(locationOptions);

      return {
        latitude: locationData.coords.latitude,
        longitude: locationData.coords.longitude,
        accuracy: locationData.coords.accuracy || undefined,
        altitude: locationData.coords.altitude || undefined,
        heading: locationData.coords.heading || undefined,
        speed: locationData.coords.speed || undefined,
        timestamp: locationData.timestamp,
      };
    } catch (error) {
      console.error('Failed to get current location:', error);
      throw {
        code: ErrorCode.LOCATION_UNAVAILABLE,
        message: 'Failed to get current location',
        details: error,
        timestamp: Date.now(),
      } as AppError;
    }
  }

  // Geofencing
  public addGeofence(tag: LocationTag): void {
    this.geofences.set(tag.id, tag);
    console.log(`Added geofence for tag: ${tag.name}`);
  }

  public removeGeofence(tagId: string): void {
    this.geofences.delete(tagId);
    console.log(`Removed geofence for tag: ${tagId}`);
  }

  public clearGeofences(): void {
    this.geofences.clear();
    console.log('Cleared all geofences');
  }

  // Event Listeners
  public onLocationUpdate(callback: (location: Location) => void): () => void {
    this.locationUpdateCallbacks.push(callback);
    return () => {
      const index = this.locationUpdateCallbacks.indexOf(callback);
      if (index > -1) {
        this.locationUpdateCallbacks.splice(index, 1);
      }
    };
  }

  public onGeofenceEvent(callback: (tagId: string, event: 'enter' | 'exit') => void): () => void {
    this.geofenceCallbacks.push(callback);
    return () => {
      const index = this.geofenceCallbacks.indexOf(callback);
      if (index > -1) {
        this.geofenceCallbacks.splice(index, 1);
      }
    };
  }

  // Utility Methods
  public calculateDistance(location1: Location, location2: Location): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (location1.latitude * Math.PI) / 180;
    const φ2 = (location2.latitude * Math.PI) / 180;
    const Δφ = ((location2.latitude - location1.latitude) * Math.PI) / 180;
    const Δλ = ((location2.longitude - location1.longitude) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distance in meters
  }

  public isLocationInRadius(center: Location, target: Location, radius: number): boolean {
    const distance = this.calculateDistance(center, target);
    return distance <= radius;
  }

  // Private Methods
  private getLocationOptions(accuracy: LocationAccuracy): ExpoLocation.LocationOptions {
    const accuracyMap = {
      [LocationAccuracy.LOW]: ExpoLocation.Accuracy.Low,
      [LocationAccuracy.BALANCED]: ExpoLocation.Accuracy.Balanced,
      [LocationAccuracy.HIGH]: ExpoLocation.Accuracy.High,
      [LocationAccuracy.BEST]: ExpoLocation.Accuracy.BestForNavigation,
    };

    return {
      accuracy: accuracyMap[accuracy],
      timeInterval: accuracy === LocationAccuracy.BEST ? 1000 : 5000,
      distanceInterval: accuracy === LocationAccuracy.LOW ? 100 : 10,
    };
  }

  private async startBackgroundLocationTask(): Promise<void> {
    const isRegistered = await TaskManager.isTaskRegisteredAsync(LOCATION_TASK_NAME);
    if (!isRegistered) {
      await ExpoLocation.startLocationUpdatesAsync(LOCATION_TASK_NAME, {
        accuracy: ExpoLocation.Accuracy.Balanced,
        timeInterval: 10000,
        distanceInterval: 50,
        foregroundService: {
          notificationTitle: 'Location Music App',
          notificationBody: 'Tracking location for music playback',
        },
      });
    }
  }

  private setupBackgroundTasks(): void {
    TaskManager.defineTask(LOCATION_TASK_NAME, ({ data, error }) => {
      if (error) {
        console.error('Background location task error:', error);
        return;
      }

      if (data) {
        const { locations } = data as any;
        if (locations && locations.length > 0) {
          const locationData = locations[0];
          const location: Location = {
            latitude: locationData.coords.latitude,
            longitude: locationData.coords.longitude,
            accuracy: locationData.coords.accuracy || undefined,
            altitude: locationData.coords.altitude || undefined,
            heading: locationData.coords.heading || undefined,
            speed: locationData.coords.speed || undefined,
            timestamp: locationData.timestamp,
          };

          this.currentLocation = location;
          this.checkGeofences(location);
        }
      }
    });
  }

  private notifyLocationUpdate(location: Location): void {
    this.locationUpdateCallbacks.forEach(callback => {
      try {
        callback(location);
      } catch (error) {
        console.error('Location update callback error:', error);
      }
    });
  }

  private checkGeofences(currentLocation: Location): void {
    this.geofences.forEach((tag, tagId) => {
      const isInside = this.isLocationInRadius(
        tag.location,
        currentLocation,
        tag.radius || 100
      );

      // Here you would implement enter/exit logic
      // This is a simplified version - you'd need to track previous state
      if (isInside) {
        this.notifyGeofenceEvent(tagId, 'enter');
      }
    });
  }

  private notifyGeofenceEvent(tagId: string, event: 'enter' | 'exit'): void {
    this.geofenceCallbacks.forEach(callback => {
      try {
        callback(tagId, event);
      } catch (error) {
        console.error('Geofence callback error:', error);
      }
    });
  }
}

export default LocationService.getInstance();
