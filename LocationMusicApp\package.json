{"name": "locationmusicapp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "expo": "~53.0.17", "expo-auth-session": "^6.2.1", "expo-av": "^15.1.7", "expo-background-fetch": "^13.1.6", "expo-build-properties": "^0.14.8", "expo-crypto": "^14.1.5", "expo-linear-gradient": "^14.1.5", "expo-location": "^18.1.6", "expo-notifications": "^0.31.4", "expo-secure-store": "^14.2.3", "expo-sqlite": "^15.2.13", "expo-status-bar": "~2.2.3", "expo-task-manager": "^13.1.6", "expo-web-browser": "^14.2.0", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.1", "react-native-maps": "^1.24.3", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1", "react-native-track-player": "^4.1.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "^19.1.8", "@types/react-native": "^0.72.8", "typescript": "^5.8.3"}, "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-track-player"]}}}}