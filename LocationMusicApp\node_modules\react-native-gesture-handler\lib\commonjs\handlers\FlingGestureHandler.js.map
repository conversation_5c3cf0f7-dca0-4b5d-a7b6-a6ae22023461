{"version": 3, "sources": ["FlingGestureHandler.ts"], "names": ["flingGestureHandlerProps", "flingHandlerName", "FlingGestureHandler", "name", "allowedProps", "baseGestureHandlerProps", "config"], "mappings": ";;;;;;;AACA;;AACA;;;;AAKO,MAAMA,wBAAwB,GAAG,CACtC,kBADsC,EAEtC,WAFsC,CAAjC;;AAmCA,MAAMC,gBAAgB,GAAG,qBAAzB;AAEP;AACA;AACA;;;;AAGA;AACA;AACA;AACA;AACO,MAAMC,mBAAmB,GAAG,4BAGjC;AACAC,EAAAA,IAAI,EAAEF,gBADN;AAEAG,EAAAA,YAAY,EAAE,CACZ,GAAGC,6CADS,EAEZ,GAAGL,wBAFS,CAFd;AAMAM,EAAAA,MAAM,EAAE;AANR,CAHiC,CAA5B", "sourcesContent": ["import type { FlingGestureHandlerEventPayload } from './GestureHandlerEventPayload';\nimport createHandler from './createHandler';\nimport {\n  BaseGestureHandlerProps,\n  baseGestureHandlerProps,\n} from './gestureHandlerCommon';\n\nexport const flingGestureHandlerProps = [\n  'numberOfPointers',\n  'direction',\n] as const;\n\nexport interface FlingGestureConfig {\n  /**\n   * Expressed allowed direction of movement. It's possible to pass one or many\n   * directions in one parameter:\n   *\n   * ```js\n   * direction={Directions.RIGHT | Directions.LEFT}\n   * ```\n   *\n   * or\n   *\n   * ```js\n   * direction={Directions.DOWN}\n   * ```\n   */\n  direction?: number;\n\n  /**\n   * Determine exact number of points required to handle the fling gesture.\n   */\n  numberOfPointers?: number;\n}\n\n/**\n * @deprecated FlingGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Fling()` instead.\n */\nexport interface FlingGestureHandlerProps\n  extends BaseGestureHandlerProps<FlingGestureHandlerEventPayload>,\n    FlingGestureConfig {}\n\nexport const flingHandlerName = 'FlingGestureHandler';\n\n/**\n * @deprecated FlingGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Fling()` instead.\n */\nexport type FlingGestureHandler = typeof FlingGestureHandler;\n\n/**\n * @deprecated FlingGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Fling()` instead.\n */\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\nexport const FlingGestureHandler = createHandler<\n  FlingGestureHandlerProps,\n  FlingGestureHandlerEventPayload\n>({\n  name: flingHandlerName,\n  allowedProps: [\n    ...baseGestureHandlerProps,\n    ...flingGestureHandlerProps,\n  ] as const,\n  config: {},\n});\n"]}