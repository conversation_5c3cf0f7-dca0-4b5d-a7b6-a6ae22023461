{"version": 3, "sources": ["utils.ts"], "names": ["numberAsInset", "value", "left", "right", "top", "bottom", "addInsets", "a", "b", "touchDataToPressEvent", "data", "timestamp", "targetId", "identifier", "id", "locationX", "x", "locationY", "y", "pageX", "absoluteX", "pageY", "absoluteY", "target", "touches", "changedTouches", "gestureToPressEvent", "event", "handlerTag", "isTouchWithinInset", "dimensions", "inset", "touch", "width", "height", "gestureToPressableEvent", "Date", "now", "pressEvent", "nativeEvent", "force", "undefined", "gestureTouchToPressableEvent", "touchesList", "allTouches", "map", "changedTouchesList", "at"], "mappings": ";;;;;;;AAYA,MAAMA,aAAa,GAAIC,KAAD,KAA4B;AAChDC,EAAAA,IAAI,EAAED,KAD0C;AAEhDE,EAAAA,KAAK,EAAEF,KAFyC;AAGhDG,EAAAA,GAAG,EAAEH,KAH2C;AAIhDI,EAAAA,MAAM,EAAEJ;AAJwC,CAA5B,CAAtB;;;;AAOA,MAAMK,SAAS,GAAG,CAACC,CAAD,EAAYC,CAAZ;AAAA;;AAAA,SAAmC;AACnDN,IAAAA,IAAI,EAAE,YAACK,CAAC,CAACL,IAAH,6CAAW,CAAX,gBAAiBM,CAAC,CAACN,IAAnB,6CAA2B,CAA3B,CAD6C;AAEnDC,IAAAA,KAAK,EAAE,aAACI,CAAC,CAACJ,KAAH,+CAAY,CAAZ,iBAAkBK,CAAC,CAACL,KAApB,+CAA6B,CAA7B,CAF4C;AAGnDC,IAAAA,GAAG,EAAE,WAACG,CAAC,CAACH,GAAH,2CAAU,CAAV,eAAgBI,CAAC,CAACJ,GAAlB,2CAAyB,CAAzB,CAH8C;AAInDC,IAAAA,MAAM,EAAE,cAACE,CAAC,CAACF,MAAH,iDAAa,CAAb,kBAAmBG,CAAC,CAACH,MAArB,iDAA+B,CAA/B;AAJ2C,GAAnC;AAAA,CAAlB;;;;AAOA,MAAMI,qBAAqB,GAAG,CAC5BC,IAD4B,EAE5BC,SAF4B,EAG5BC,QAH4B,MAIH;AACzBC,EAAAA,UAAU,EAAEH,IAAI,CAACI,EADQ;AAEzBC,EAAAA,SAAS,EAAEL,IAAI,CAACM,CAFS;AAGzBC,EAAAA,SAAS,EAAEP,IAAI,CAACQ,CAHS;AAIzBC,EAAAA,KAAK,EAAET,IAAI,CAACU,SAJa;AAKzBC,EAAAA,KAAK,EAAEX,IAAI,CAACY,SALa;AAMzBC,EAAAA,MAAM,EAAEX,QANiB;AAOzBD,EAAAA,SAAS,EAAEA,SAPc;AAQzBa,EAAAA,OAAO,EAAE,EARgB;AAQZ;AACbC,EAAAA,cAAc,EAAE,EATS,CASL;;AATK,CAJG,CAA9B;;AAgBA,MAAMC,mBAAmB,GAAG,CAC1BC,KAD0B,EAI1BhB,SAJ0B,EAK1BC,QAL0B,MAMD;AACzBC,EAAAA,UAAU,EAAEc,KAAK,CAACC,UADO;AAEzBb,EAAAA,SAAS,EAAEY,KAAK,CAACX,CAFQ;AAGzBC,EAAAA,SAAS,EAAEU,KAAK,CAACT,CAHQ;AAIzBC,EAAAA,KAAK,EAAEQ,KAAK,CAACP,SAJY;AAKzBC,EAAAA,KAAK,EAAEM,KAAK,CAACL,SALY;AAMzBC,EAAAA,MAAM,EAAEX,QANiB;AAOzBD,EAAAA,SAAS,EAAEA,SAPc;AAQzBa,EAAAA,OAAO,EAAE,EARgB;AAQZ;AACbC,EAAAA,cAAc,EAAE,EATS,CASL;;AATK,CANC,CAA5B;;AAkBA,MAAMI,kBAAkB,GAAG,CACzBC,UADyB,EAEzBC,KAFyB,EAGzBC,KAHyB;AAAA;;AAAA,SAKzB,aAACA,KAAD,aAACA,KAAD,uBAACA,KAAK,CAAEhB,CAAR,+CAAa,CAAb,IAAkB,iBAACe,KAAK,CAAC5B,KAAP,uDAAgB,CAAhB,IAAqB2B,UAAU,CAACG,KAAlD,IACA,aAACD,KAAD,aAACA,KAAD,uBAACA,KAAK,CAAEd,CAAR,+CAAa,CAAb,IAAkB,kBAACa,KAAK,CAAC1B,MAAP,yDAAiB,CAAjB,IAAsByB,UAAU,CAACI,MADnD,IAEA,cAACF,KAAD,aAACA,KAAD,uBAACA,KAAK,CAAEhB,CAAR,iDAAa,CAAb,IAAkB,iBAAEe,KAAK,CAAC7B,IAAR,qDAAgB,CAAhB,CAFlB,IAGA,cAAC8B,KAAD,aAACA,KAAD,uBAACA,KAAK,CAAEd,CAAR,iDAAa,CAAb,IAAkB,gBAAEa,KAAK,CAAC3B,GAAR,mDAAe,CAAf,CARO;AAAA,CAA3B;;;;AAUA,MAAM+B,uBAAuB,GAC3BR,KAD8B,IAIX;AACnB,QAAMhB,SAAS,GAAGyB,IAAI,CAACC,GAAL,EAAlB,CADmB,CAGnB;;AACA,QAAMzB,QAAQ,GAAG,CAAjB;AAEA,QAAM0B,UAAU,GAAGZ,mBAAmB,CAACC,KAAD,EAAQhB,SAAR,EAAmBC,QAAnB,CAAtC;AAEA,SAAO;AACL2B,IAAAA,WAAW,EAAE;AACXf,MAAAA,OAAO,EAAE,CAACc,UAAD,CADE;AAEXb,MAAAA,cAAc,EAAE,CAACa,UAAD,CAFL;AAGXzB,MAAAA,UAAU,EAAEyB,UAAU,CAACzB,UAHZ;AAIXE,MAAAA,SAAS,EAAEY,KAAK,CAACX,CAJN;AAKXC,MAAAA,SAAS,EAAEU,KAAK,CAACT,CALN;AAMXC,MAAAA,KAAK,EAAEQ,KAAK,CAACP,SANF;AAOXC,MAAAA,KAAK,EAAEM,KAAK,CAACL,SAPF;AAQXC,MAAAA,MAAM,EAAEX,QARG;AASXD,MAAAA,SAAS,EAAEA,SATA;AAUX6B,MAAAA,KAAK,EAAEC;AAVI;AADR,GAAP;AAcD,CA1BD;;;;AA4BA,MAAMC,4BAA4B,GAChCf,KADmC,IAEhB;AAAA;;AACnB,QAAMhB,SAAS,GAAGyB,IAAI,CAACC,GAAL,EAAlB,CADmB,CAGnB;;AACA,QAAMzB,QAAQ,GAAG,CAAjB;AAEA,QAAM+B,WAAW,GAAGhB,KAAK,CAACiB,UAAN,CAAiBC,GAAjB,CAAsBb,KAAD,IACvCvB,qBAAqB,CAACuB,KAAD,EAAQrB,SAAR,EAAmBC,QAAnB,CADH,CAApB;AAGA,QAAMkC,kBAAkB,GAAGnB,KAAK,CAACF,cAAN,CAAqBoB,GAArB,CAA0Bb,KAAD,IAClDvB,qBAAqB,CAACuB,KAAD,EAAQrB,SAAR,EAAmBC,QAAnB,CADI,CAA3B;AAIA,SAAO;AACL2B,IAAAA,WAAW,EAAE;AACXf,MAAAA,OAAO,EAAEmB,WADE;AAEXlB,MAAAA,cAAc,EAAEqB,kBAFL;AAGXjC,MAAAA,UAAU,EAAEc,KAAK,CAACC,UAHP;AAIXb,MAAAA,SAAS,mDAAEY,KAAK,CAACiB,UAAN,CAAiBG,EAAjB,CAAoB,CAApB,CAAF,yDAAE,qBAAwB/B,CAA1B,yEAA+B,CAAC,CAJ9B;AAKXC,MAAAA,SAAS,qDAAEU,KAAK,CAACiB,UAAN,CAAiBG,EAAjB,CAAoB,CAApB,CAAF,0DAAE,sBAAwB7B,CAA1B,2EAA+B,CAAC,CAL9B;AAMXC,MAAAA,KAAK,qDAAEQ,KAAK,CAACiB,UAAN,CAAiBG,EAAjB,CAAoB,CAApB,CAAF,0DAAE,sBAAwB3B,SAA1B,2EAAuC,CAAC,CANlC;AAOXC,MAAAA,KAAK,qDAAEM,KAAK,CAACiB,UAAN,CAAiBG,EAAjB,CAAoB,CAApB,CAAF,0DAAE,sBAAwBzB,SAA1B,2EAAuC,CAAC,CAPlC;AAQXC,MAAAA,MAAM,EAAEX,QARG;AASXD,MAAAA,SAAS,EAAEA,SATA;AAUX6B,MAAAA,KAAK,EAAEC;AAVI;AADR,GAAP;AAcD,CA7BD", "sourcesContent": ["import { Insets } from 'react-native';\nimport {\n  HoverGestureHandlerEventPayload,\n  LongPressGestureHandlerEventPayload,\n} from '../../handlers/GestureHandlerEventPayload';\nimport {\n  TouchData,\n  GestureStateChangeEvent,\n  GestureTouchEvent,\n} from '../../handlers/gestureHandlerCommon';\nimport { InnerPressableEvent, PressableEvent } from './PressableProps';\n\nconst numberAsInset = (value: number): Insets => ({\n  left: value,\n  right: value,\n  top: value,\n  bottom: value,\n});\n\nconst addInsets = (a: Insets, b: Insets): Insets => ({\n  left: (a.left ?? 0) + (b.left ?? 0),\n  right: (a.right ?? 0) + (b.right ?? 0),\n  top: (a.top ?? 0) + (b.top ?? 0),\n  bottom: (a.bottom ?? 0) + (b.bottom ?? 0),\n});\n\nconst touchDataToPressEvent = (\n  data: TouchData,\n  timestamp: number,\n  targetId: number\n): InnerPressableEvent => ({\n  identifier: data.id,\n  locationX: data.x,\n  locationY: data.y,\n  pageX: data.absoluteX,\n  pageY: data.absoluteY,\n  target: targetId,\n  timestamp: timestamp,\n  touches: [], // Always empty - legacy compatibility\n  changedTouches: [], // Always empty - legacy compatibility\n});\n\nconst gestureToPressEvent = (\n  event: GestureStateChangeEvent<\n    HoverGestureHandlerEventPayload | LongPressGestureHandlerEventPayload\n  >,\n  timestamp: number,\n  targetId: number\n): InnerPressableEvent => ({\n  identifier: event.handlerTag,\n  locationX: event.x,\n  locationY: event.y,\n  pageX: event.absoluteX,\n  pageY: event.absoluteY,\n  target: targetId,\n  timestamp: timestamp,\n  touches: [], // Always empty - legacy compatibility\n  changedTouches: [], // Always empty - legacy compatibility\n});\n\nconst isTouchWithinInset = (\n  dimensions: { width: number; height: number },\n  inset: Insets,\n  touch?: TouchData\n) =>\n  (touch?.x ?? 0) < (inset.right ?? 0) + dimensions.width &&\n  (touch?.y ?? 0) < (inset.bottom ?? 0) + dimensions.height &&\n  (touch?.x ?? 0) > -(inset.left ?? 0) &&\n  (touch?.y ?? 0) > -(inset.top ?? 0);\n\nconst gestureToPressableEvent = (\n  event: GestureStateChangeEvent<\n    HoverGestureHandlerEventPayload | LongPressGestureHandlerEventPayload\n  >\n): PressableEvent => {\n  const timestamp = Date.now();\n\n  // As far as I can see, there isn't a conventional way of getting targetId with the data we get\n  const targetId = 0;\n\n  const pressEvent = gestureToPressEvent(event, timestamp, targetId);\n\n  return {\n    nativeEvent: {\n      touches: [pressEvent],\n      changedTouches: [pressEvent],\n      identifier: pressEvent.identifier,\n      locationX: event.x,\n      locationY: event.y,\n      pageX: event.absoluteX,\n      pageY: event.absoluteY,\n      target: targetId,\n      timestamp: timestamp,\n      force: undefined,\n    },\n  };\n};\n\nconst gestureTouchToPressableEvent = (\n  event: GestureTouchEvent\n): PressableEvent => {\n  const timestamp = Date.now();\n\n  // As far as I can see, there isn't a conventional way of getting targetId with the data we get\n  const targetId = 0;\n\n  const touchesList = event.allTouches.map((touch: TouchData) =>\n    touchDataToPressEvent(touch, timestamp, targetId)\n  );\n  const changedTouchesList = event.changedTouches.map((touch: TouchData) =>\n    touchDataToPressEvent(touch, timestamp, targetId)\n  );\n\n  return {\n    nativeEvent: {\n      touches: touchesList,\n      changedTouches: changedTouchesList,\n      identifier: event.handlerTag,\n      locationX: event.allTouches.at(0)?.x ?? -1,\n      locationY: event.allTouches.at(0)?.y ?? -1,\n      pageX: event.allTouches.at(0)?.absoluteX ?? -1,\n      pageY: event.allTouches.at(0)?.absoluteY ?? -1,\n      target: targetId,\n      timestamp: timestamp,\n      force: undefined,\n    },\n  };\n};\n\nexport {\n  numberAsInset,\n  addInsets,\n  isTouchWithinInset,\n  gestureToPressableEvent,\n  gestureTouchToPressableEvent,\n};\n"]}