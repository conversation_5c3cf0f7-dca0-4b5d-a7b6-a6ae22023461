import * as AuthSession from 'expo-auth-session';
import * as Web<PERSON>rowser from 'expo-web-browser';
import * as SecureStore from 'expo-secure-store';
import { Track, Playlist, MusicService, ConnectedService, AppError, ErrorCode } from '../types';

WebBrowser.maybeCompleteAuthSession();

interface SpotifyAuthConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scopes: string[];
}

interface SpotifyTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
  scope: string;
}

interface SpotifyTrack {
  id: string;
  name: string;
  artists: Array<{ name: string }>;
  album: {
    name: string;
    images: Array<{ url: string; height: number; width: number }>;
  };
  duration_ms: number;
  preview_url?: string;
  external_urls: {
    spotify: string;
  };
}

interface SpotifyPlaylist {
  id: string;
  name: string;
  description?: string;
  images: Array<{ url: string; height: number; width: number }>;
  tracks: {
    items: Array<{ track: SpotifyTrack }>;
    total: number;
  };
  external_urls: {
    spotify: string;
  };
}

export class SpotifyService {
  private static instance: SpotifyService;
  private config: SpotifyAuthConfig;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private tokenExpiresAt: number | null = null;

  private constructor() {
    this.config = {
      clientId: process.env.EXPO_PUBLIC_SPOTIFY_CLIENT_ID || '',
      clientSecret: process.env.EXPO_PUBLIC_SPOTIFY_CLIENT_SECRET || '',
      redirectUri: AuthSession.makeRedirectUri({ useProxy: true }),
      scopes: [
        'user-read-private',
        'user-read-email',
        'user-library-read',
        'user-library-modify',
        'playlist-read-private',
        'playlist-read-collaborative',
        'playlist-modify-public',
        'playlist-modify-private',
        'user-read-playback-state',
        'user-modify-playback-state',
        'user-read-currently-playing',
        'streaming',
      ],
    };
  }

  public static getInstance(): SpotifyService {
    if (!SpotifyService.instance) {
      SpotifyService.instance = new SpotifyService();
    }
    return SpotifyService.instance;
  }

  // Authentication
  public async authenticate(): Promise<ConnectedService> {
    try {
      const discovery = {
        authorizationEndpoint: 'https://accounts.spotify.com/authorize',
        tokenEndpoint: 'https://accounts.spotify.com/api/token',
      };

      const request = new AuthSession.AuthRequest({
        clientId: this.config.clientId,
        scopes: this.config.scopes,
        usePKCE: true,
        redirectUri: this.config.redirectUri,
        responseType: AuthSession.ResponseType.Code,
      });

      const result = await request.promptAsync(discovery);

      if (result.type === 'success') {
        const tokenResult = await AuthSession.exchangeCodeAsync(
          {
            clientId: this.config.clientId,
            code: result.params.code,
            redirectUri: this.config.redirectUri,
            extraParams: {
              code_verifier: request.codeVerifier || '',
            },
          },
          discovery
        );

        this.accessToken = tokenResult.accessToken;
        this.refreshToken = tokenResult.refreshToken || null;
        this.tokenExpiresAt = tokenResult.expiresIn 
          ? Date.now() + tokenResult.expiresIn * 1000 
          : null;

        // Store tokens securely
        await this.storeTokens();

        // Get user info
        const userInfo = await this.getCurrentUser();

        const connectedService: ConnectedService = {
          service: MusicService.SPOTIFY,
          accessToken: this.accessToken,
          refreshToken: this.refreshToken,
          expiresAt: this.tokenExpiresAt,
          userId: userInfo.id,
          isActive: true,
          connectedAt: Date.now(),
        };

        return connectedService;
      } else {
        throw new Error('Authentication cancelled or failed');
      }
    } catch (error) {
      console.error('Spotify authentication error:', error);
      throw {
        code: ErrorCode.MUSIC_SERVICE_AUTH_FAILED,
        message: 'Failed to authenticate with Spotify',
        details: error,
        timestamp: Date.now(),
      } as AppError;
    }
  }

  public async loadStoredTokens(): Promise<boolean> {
    try {
      const accessToken = await SecureStore.getItemAsync('spotify_access_token');
      const refreshToken = await SecureStore.getItemAsync('spotify_refresh_token');
      const expiresAt = await SecureStore.getItemAsync('spotify_expires_at');

      if (accessToken) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.tokenExpiresAt = expiresAt ? parseInt(expiresAt) : null;

        // Check if token is expired and refresh if needed
        if (this.tokenExpiresAt && Date.now() >= this.tokenExpiresAt) {
          await this.refreshAccessToken();
        }

        return true;
      }

      return false;
    } catch (error) {
      console.error('Error loading stored tokens:', error);
      return false;
    }
  }

  public async disconnect(): Promise<void> {
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiresAt = null;

    await SecureStore.deleteItemAsync('spotify_access_token');
    await SecureStore.deleteItemAsync('spotify_refresh_token');
    await SecureStore.deleteItemAsync('spotify_expires_at');
  }

  // API Methods
  public async getCurrentUser(): Promise<any> {
    const response = await this.makeAuthenticatedRequest('https://api.spotify.com/v1/me');
    return response;
  }

  public async getUserPlaylists(limit: number = 50): Promise<Playlist[]> {
    const response = await this.makeAuthenticatedRequest(
      `https://api.spotify.com/v1/me/playlists?limit=${limit}`
    );

    const playlists: Playlist[] = [];
    for (const spotifyPlaylist of response.items) {
      const tracks = await this.getPlaylistTracks(spotifyPlaylist.id);
      
      playlists.push({
        id: `spotify_${spotifyPlaylist.id}`,
        name: spotifyPlaylist.name,
        description: spotifyPlaylist.description,
        service: MusicService.SPOTIFY,
        servicePlaylistId: spotifyPlaylist.id,
        artwork: spotifyPlaylist.images?.[0]?.url,
        tracks,
      });
    }

    return playlists;
  }

  public async getPlaylistTracks(playlistId: string): Promise<Track[]> {
    const response = await this.makeAuthenticatedRequest(
      `https://api.spotify.com/v1/playlists/${playlistId}/tracks`
    );

    return response.items
      .filter((item: any) => item.track && item.track.id)
      .map((item: any) => this.convertSpotifyTrackToTrack(item.track));
  }

  public async searchTracks(query: string, limit: number = 20): Promise<Track[]> {
    const response = await this.makeAuthenticatedRequest(
      `https://api.spotify.com/v1/search?q=${encodeURIComponent(query)}&type=track&limit=${limit}`
    );

    return response.tracks.items.map((track: SpotifyTrack) => 
      this.convertSpotifyTrackToTrack(track)
    );
  }

  public async getCurrentlyPlaying(): Promise<Track | null> {
    try {
      const response = await this.makeAuthenticatedRequest(
        'https://api.spotify.com/v1/me/player/currently-playing'
      );

      if (response && response.item) {
        return this.convertSpotifyTrackToTrack(response.item);
      }

      return null;
    } catch (error) {
      console.error('Error getting currently playing track:', error);
      return null;
    }
  }

  public async playTrack(trackId: string, deviceId?: string): Promise<void> {
    const body: any = {
      uris: [`spotify:track:${trackId}`],
    };

    if (deviceId) {
      body.device_id = deviceId;
    }

    await this.makeAuthenticatedRequest(
      'https://api.spotify.com/v1/me/player/play',
      {
        method: 'PUT',
        body: JSON.stringify(body),
      }
    );
  }

  public async playPlaylist(playlistId: string, deviceId?: string): Promise<void> {
    const body: any = {
      context_uri: `spotify:playlist:${playlistId}`,
    };

    if (deviceId) {
      body.device_id = deviceId;
    }

    await this.makeAuthenticatedRequest(
      'https://api.spotify.com/v1/me/player/play',
      {
        method: 'PUT',
        body: JSON.stringify(body),
      }
    );
  }

  public async pausePlayback(): Promise<void> {
    await this.makeAuthenticatedRequest(
      'https://api.spotify.com/v1/me/player/pause',
      { method: 'PUT' }
    );
  }

  public async resumePlayback(): Promise<void> {
    await this.makeAuthenticatedRequest(
      'https://api.spotify.com/v1/me/player/play',
      { method: 'PUT' }
    );
  }

  public async skipToNext(): Promise<void> {
    await this.makeAuthenticatedRequest(
      'https://api.spotify.com/v1/me/player/next',
      { method: 'POST' }
    );
  }

  public async skipToPrevious(): Promise<void> {
    await this.makeAuthenticatedRequest(
      'https://api.spotify.com/v1/me/player/previous',
      { method: 'POST' }
    );
  }

  public async setVolume(volumePercent: number): Promise<void> {
    await this.makeAuthenticatedRequest(
      `https://api.spotify.com/v1/me/player/volume?volume_percent=${volumePercent}`,
      { method: 'PUT' }
    );
  }

  // Private Methods
  private async storeTokens(): Promise<void> {
    if (this.accessToken) {
      await SecureStore.setItemAsync('spotify_access_token', this.accessToken);
    }
    if (this.refreshToken) {
      await SecureStore.setItemAsync('spotify_refresh_token', this.refreshToken);
    }
    if (this.tokenExpiresAt) {
      await SecureStore.setItemAsync('spotify_expires_at', this.tokenExpiresAt.toString());
    }
  }

  private async refreshAccessToken(): Promise<void> {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await fetch('https://accounts.spotify.com/api/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${btoa(`${this.config.clientId}:${this.config.clientSecret}`)}`,
        },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token: this.refreshToken,
        }),
      });

      const tokenData: SpotifyTokenResponse = await response.json();

      this.accessToken = tokenData.access_token;
      this.tokenExpiresAt = Date.now() + tokenData.expires_in * 1000;

      if (tokenData.refresh_token) {
        this.refreshToken = tokenData.refresh_token;
      }

      await this.storeTokens();
    } catch (error) {
      console.error('Error refreshing access token:', error);
      throw error;
    }
  }

  private async makeAuthenticatedRequest(url: string, options: RequestInit = {}): Promise<any> {
    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    // Check if token is expired and refresh if needed
    if (this.tokenExpiresAt && Date.now() >= this.tokenExpiresAt) {
      await this.refreshAccessToken();
    }

    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      if (response.status === 401) {
        // Token might be invalid, try to refresh
        await this.refreshAccessToken();
        
        // Retry the request
        const retryResponse = await fetch(url, {
          ...options,
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
            ...options.headers,
          },
        });

        if (!retryResponse.ok) {
          throw new Error(`Spotify API error: ${retryResponse.status} ${retryResponse.statusText}`);
        }

        return retryResponse.status === 204 ? null : await retryResponse.json();
      }

      throw new Error(`Spotify API error: ${response.status} ${response.statusText}`);
    }

    return response.status === 204 ? null : await response.json();
  }

  private convertSpotifyTrackToTrack(spotifyTrack: SpotifyTrack): Track {
    return {
      id: `spotify_${spotifyTrack.id}`,
      title: spotifyTrack.name,
      artist: spotifyTrack.artists.map(artist => artist.name).join(', '),
      album: spotifyTrack.album.name,
      duration: Math.floor(spotifyTrack.duration_ms / 1000),
      artwork: spotifyTrack.album.images?.[0]?.url,
      service: MusicService.SPOTIFY,
      serviceTrackId: spotifyTrack.id,
      previewUrl: spotifyTrack.preview_url,
    };
  }
}

export default SpotifyService.getInstance();
