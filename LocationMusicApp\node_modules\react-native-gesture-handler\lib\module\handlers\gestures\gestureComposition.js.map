{"version": 3, "sources": ["gestureComposition.ts"], "names": ["BaseGesture", "Gesture", "extendRelation", "currentRelation", "extendWith", "undefined", "ComposedGesture", "constructor", "gestures", "prepareSingleGesture", "gesture", "simultaneousGestures", "requireGesturesToFail", "newConfig", "config", "simultaneousWith", "requireToFail", "prepare", "initialize", "toGestureArray", "flatMap", "SimultaneousGesture", "simultaneousArrays", "map", "filter", "x", "i", "length", "ExclusiveGesture", "gestureArrays", "concat"], "mappings": ";;AAAA,SAASA,WAAT,EAAsBC,OAAtB,QAA8D,WAA9D;;AAEA,SAASC,cAAT,CACEC,eADF,EAEEC,UAFF,EAGE;AACA,MAAID,eAAe,KAAKE,SAAxB,EAAmC;AACjC,WAAO,CAAC,GAAGD,UAAJ,CAAP;AACD,GAFD,MAEO;AACL,WAAO,CAAC,GAAGD,eAAJ,EAAqB,GAAGC,UAAxB,CAAP;AACD;AACF;;AAED,OAAO,MAAME,eAAN,SAA8BL,OAA9B,CAAsC;AAK3CM,EAAAA,WAAW,CAAC,GAAGC,QAAJ,EAAyB;AAClC;;AADkC,sCAJJ,EAII;;AAAA,kDAHY,EAGZ;;AAAA,mDAFa,EAEb;;AAElC,SAAKA,QAAL,GAAgBA,QAAhB;AACD;;AAESC,EAAAA,oBAAoB,CAC5BC,OAD4B,EAE5BC,oBAF4B,EAG5BC,qBAH4B,EAI5B;AACA,QAAIF,OAAO,YAAYV,WAAvB,EAAoC;AAClC,YAAMa,SAAS,GAAG,EAAE,GAAGH,OAAO,CAACI;AAAb,OAAlB,CADkC,CAGlC;AACA;;AACAD,MAAAA,SAAS,CAACE,gBAAV,GAA6Bb,cAAc,CACzCW,SAAS,CAACE,gBAD+B,EAEzCJ,oBAFyC,CAA3C;AAIAE,MAAAA,SAAS,CAACG,aAAV,GAA0Bd,cAAc,CACtCW,SAAS,CAACG,aAD4B,EAEtCJ,qBAFsC,CAAxC;AAKAF,MAAAA,OAAO,CAACI,MAAR,GAAiBD,SAAjB;AACD,KAfD,MAeO,IAAIH,OAAO,YAAYJ,eAAvB,EAAwC;AAC7CI,MAAAA,OAAO,CAACC,oBAAR,GAA+BA,oBAA/B;AACAD,MAAAA,OAAO,CAACE,qBAAR,GAAgCA,qBAAhC;AACAF,MAAAA,OAAO,CAACO,OAAR;AACD;AACF;;AAEDA,EAAAA,OAAO,GAAG;AACR,SAAK,MAAMP,OAAX,IAAsB,KAAKF,QAA3B,EAAqC;AACnC,WAAKC,oBAAL,CACEC,OADF,EAEE,KAAKC,oBAFP,EAGE,KAAKC,qBAHP;AAKD;AACF;;AAEDM,EAAAA,UAAU,GAAG;AACX,SAAK,MAAMR,OAAX,IAAsB,KAAKF,QAA3B,EAAqC;AACnCE,MAAAA,OAAO,CAACQ,UAAR;AACD;AACF;;AAEDC,EAAAA,cAAc,GAAkB;AAC9B,WAAO,KAAKX,QAAL,CAAcY,OAAd,CAAuBV,OAAD,IAAaA,OAAO,CAACS,cAAR,EAAnC,CAAP;AACD;;AAvD0C;AA0D7C,OAAO,MAAME,mBAAN,SAAkCf,eAAlC,CAAkD;AACvDW,EAAAA,OAAO,GAAG;AACR;AACA;AACA,UAAMK,kBAAkB,GAAG,KAAKd,QAAL,CAAce,GAAd,CAAmBb,OAAD,IAC3C;AACA,SAAKF,QAAL,CACE;AADF,KAEGgB,MAFH,CAEWC,CAAD,IAAOA,CAAC,KAAKf,OAFvB,EAGE;AACA;AACA;AACA;AANF,KAOGU,OAPH,CAOYK,CAAD,IAAOA,CAAC,CAACN,cAAF,EAPlB,CAFyB,CAA3B;;AAYA,SAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlB,QAAL,CAAcmB,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;AAC7C,WAAKjB,oBAAL,CACE,KAAKD,QAAL,CAAckB,CAAd,CADF,EAEEJ,kBAAkB,CAACI,CAAD,CAFpB,EAGE,KAAKd,qBAHP;AAKD;AACF;;AAvBsD;AA0BzD,OAAO,MAAMgB,gBAAN,SAA+BtB,eAA/B,CAA+C;AACpDW,EAAAA,OAAO,GAAG;AACR;AACA;AACA,UAAMY,aAAa,GAAG,KAAKrB,QAAL,CAAce,GAAd,CAAmBb,OAAD,IACtCA,OAAO,CAACS,cAAR,EADoB,CAAtB;AAIA,QAAIH,aAA4B,GAAG,EAAnC;;AAEA,SAAK,IAAIU,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlB,QAAL,CAAcmB,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;AAC7C,WAAKjB,oBAAL,CACE,KAAKD,QAAL,CAAckB,CAAd,CADF,EAEE,KAAKf,oBAFP,EAGE,KAAKC,qBAAL,CAA2BkB,MAA3B,CAAkCd,aAAlC,CAHF,EAD6C,CAO7C;;AACAA,MAAAA,aAAa,GAAGA,aAAa,CAACc,MAAd,CAAqBD,aAAa,CAACH,CAAD,CAAlC,CAAhB;AACD;AACF;;AApBmD", "sourcesContent": ["import { BaseGesture, Gesture, GestureRef, GestureType } from './gesture';\n\nfunction extendRelation(\n  currentRelation: GestureRef[] | undefined,\n  extendWith: GestureType[]\n) {\n  if (currentRelation === undefined) {\n    return [...extendWith];\n  } else {\n    return [...currentRelation, ...extendWith];\n  }\n}\n\nexport class ComposedGesture extends Gesture {\n  protected gestures: Gesture[] = [];\n  protected simultaneousGestures: GestureType[] = [];\n  protected requireGesturesToFail: GestureType[] = [];\n\n  constructor(...gestures: Gesture[]) {\n    super();\n    this.gestures = gestures;\n  }\n\n  protected prepareSingleGesture(\n    gesture: Gesture,\n    simultaneousGestures: GestureType[],\n    requireGesturesToFail: GestureType[]\n  ) {\n    if (gesture instanceof BaseGesture) {\n      const newConfig = { ...gesture.config };\n\n      // No need to extend `blocksHandlers` here, because it's not changed in composition.\n      // The same effect is achieved by reversing the order of 2 gestures in `Exclusive`\n      newConfig.simultaneousWith = extendRelation(\n        newConfig.simultaneousWith,\n        simultaneousGestures\n      );\n      newConfig.requireToFail = extendRelation(\n        newConfig.requireToFail,\n        requireGesturesToFail\n      );\n\n      gesture.config = newConfig;\n    } else if (gesture instanceof ComposedGesture) {\n      gesture.simultaneousGestures = simultaneousGestures;\n      gesture.requireGesturesToFail = requireGesturesToFail;\n      gesture.prepare();\n    }\n  }\n\n  prepare() {\n    for (const gesture of this.gestures) {\n      this.prepareSingleGesture(\n        gesture,\n        this.simultaneousGestures,\n        this.requireGesturesToFail\n      );\n    }\n  }\n\n  initialize() {\n    for (const gesture of this.gestures) {\n      gesture.initialize();\n    }\n  }\n\n  toGestureArray(): GestureType[] {\n    return this.gestures.flatMap((gesture) => gesture.toGestureArray());\n  }\n}\n\nexport class SimultaneousGesture extends ComposedGesture {\n  prepare() {\n    // This piece of magic works something like this:\n    // for every gesture in the array\n    const simultaneousArrays = this.gestures.map((gesture) =>\n      // we take the array it's in\n      this.gestures\n        // and make a copy without it\n        .filter((x) => x !== gesture)\n        // then we flatmap the result to get list of raw (not composed) gestures\n        // this way we don't make the gestures simultaneous with themselves, which is\n        // important when the gesture is `ExclusiveGesture` - we don't want to make\n        // exclusive gestures simultaneous\n        .flatMap((x) => x.toGestureArray())\n    );\n\n    for (let i = 0; i < this.gestures.length; i++) {\n      this.prepareSingleGesture(\n        this.gestures[i],\n        simultaneousArrays[i],\n        this.requireGesturesToFail\n      );\n    }\n  }\n}\n\nexport class ExclusiveGesture extends ComposedGesture {\n  prepare() {\n    // Transforms the array of gestures into array of grouped raw (not composed) gestures\n    // i.e. [gesture1, gesture2, ComposedGesture(gesture3, gesture4)] -> [[gesture1], [gesture2], [gesture3, gesture4]]\n    const gestureArrays = this.gestures.map((gesture) =>\n      gesture.toGestureArray()\n    );\n\n    let requireToFail: GestureType[] = [];\n\n    for (let i = 0; i < this.gestures.length; i++) {\n      this.prepareSingleGesture(\n        this.gestures[i],\n        this.simultaneousGestures,\n        this.requireGesturesToFail.concat(requireToFail)\n      );\n\n      // Every group gets to wait for all groups before it\n      requireToFail = requireToFail.concat(gestureArrays[i]);\n    }\n  }\n}\n\nexport type ComposedGestureType = InstanceType<typeof ComposedGesture>;\nexport type RaceGestureType = ComposedGestureType;\nexport type SimultaneousGestureType = InstanceType<typeof SimultaneousGesture>;\nexport type ExclusiveGestureType = InstanceType<typeof ExclusiveGesture>;\n"]}