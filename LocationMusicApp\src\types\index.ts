// Core Location Types
export interface Location {
  latitude: number;
  longitude: number;
  accuracy?: number;
  altitude?: number;
  heading?: number;
  speed?: number;
  timestamp: number;
}

export interface LocationRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

// Music Service Types
export enum MusicService {
  SPOTIFY = 'spotify',
  APPLE_MUSIC = 'apple_music',
  YOUTUBE_MUSIC = 'youtube_music',
}

export interface Track {
  id: string;
  title: string;
  artist: string;
  album?: string;
  duration?: number;
  artwork?: string;
  service: MusicService;
  serviceTrackId: string;
  previewUrl?: string;
}

export interface Playlist {
  id: string;
  name: string;
  description?: string;
  tracks: Track[];
  service: MusicService;
  servicePlaylistId: string;
  artwork?: string;
}

// Location Tag Types
export enum TagType {
  RADIUS = 'radius',
  ROUTE_SEGMENT = 'route_segment',
}

export enum TagCategory {
  DRIVING = 'driving_music',
  WORKOUT = 'workout_spots',
  RELAXATION = 'relaxation_zones',
  STUDY = 'study_spots',
  PARTY = 'party_zones',
  CUSTOM = 'custom',
}

export interface LocationTag {
  id: string;
  name: string;
  description?: string;
  location: Location;
  type: TagType;
  category: TagCategory;
  radius?: number; // in meters, for radius-based tags
  routePoints?: Location[]; // for route segment tags
  tracks: Track[];
  playlists: Playlist[];
  metadata: TagMetadata;
  isPublic: boolean;
  userId: string;
  createdAt: number;
  updatedAt: number;
}

export interface TagMetadata {
  timestamp: number;
  weather?: WeatherCondition;
  userNotes?: string;
  playCount: number;
  lastPlayed?: number;
  userRating?: number; // 1-5 stars
}

export interface WeatherCondition {
  temperature: number;
  condition: string; // sunny, cloudy, rainy, etc.
  humidity?: number;
  windSpeed?: number;
}

// Playback Types
export interface PlaybackPreferences {
  autoPlayOnLocationEnter: boolean;
  fadeInDuration: number; // in milliseconds
  fadeOutDuration: number; // in milliseconds
  overlapHandling: OverlapHandlingMode;
  proximityThreshold: number; // in meters
  batteryOptimization: boolean;
}

export enum OverlapHandlingMode {
  MANUAL_SELECTION = 'manual_selection',
  MOST_RECENT = 'most_recent',
  HIGHEST_RATED = 'highest_rated',
  CLOSEST_PROXIMITY = 'closest_proximity',
  MOST_PLAYED = 'most_played',
}

// User and Privacy Types
export interface User {
  id: string;
  email: string;
  displayName?: string;
  avatar?: string;
  preferences: UserPreferences;
  connectedServices: ConnectedService[];
  createdAt: number;
}

export interface UserPreferences {
  playback: PlaybackPreferences;
  privacy: PrivacySettings;
  locationAccuracy: LocationAccuracy;
  notifications: NotificationSettings;
}

export interface PrivacySettings {
  sharePublicTags: boolean;
  allowNearbyTagDiscovery: boolean;
  shareLocationWithFriends: boolean;
  anonymousUsageData: boolean;
}

export enum LocationAccuracy {
  LOW = 'low',
  BALANCED = 'balanced',
  HIGH = 'high',
  BEST = 'best',
}

export interface NotificationSettings {
  locationBasedPlayback: boolean;
  newNearbyTags: boolean;
  friendActivity: boolean;
  systemUpdates: boolean;
}

// Music Service Integration Types
export interface ConnectedService {
  service: MusicService;
  accessToken: string;
  refreshToken?: string;
  expiresAt?: number;
  userId: string;
  isActive: boolean;
  connectedAt: number;
}

export interface ServiceAuthConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scopes: string[];
}

// Map and UI Types
export interface MapMarker {
  id: string;
  coordinate: Location;
  title: string;
  description?: string;
  type: 'user_tag' | 'public_tag' | 'current_location';
  category?: TagCategory;
  isActive?: boolean;
}

export interface FilterOptions {
  categories: TagCategory[];
  services: MusicService[];
  dateRange?: {
    start: number;
    end: number;
  };
  radiusFilter?: {
    center: Location;
    radius: number;
  };
  showPublicTags: boolean;
  showOwnTags: boolean;
}

// Background Task Types
export interface BackgroundTaskConfig {
  locationTracking: boolean;
  geofenceMonitoring: boolean;
  musicCaching: boolean;
  dataSync: boolean;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
}

export enum ErrorCode {
  LOCATION_PERMISSION_DENIED = 'LOCATION_PERMISSION_DENIED',
  LOCATION_UNAVAILABLE = 'LOCATION_UNAVAILABLE',
  MUSIC_SERVICE_AUTH_FAILED = 'MUSIC_SERVICE_AUTH_FAILED',
  MUSIC_SERVICE_UNAVAILABLE = 'MUSIC_SERVICE_UNAVAILABLE',
  NETWORK_ERROR = 'NETWORK_ERROR',
  STORAGE_ERROR = 'STORAGE_ERROR',
  PLAYBACK_ERROR = 'PLAYBACK_ERROR',
}
