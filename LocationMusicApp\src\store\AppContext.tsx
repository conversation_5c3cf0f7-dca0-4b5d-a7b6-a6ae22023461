import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { 
  User, 
  LocationTag, 
  Location, 
  Track, 
  Playlist, 
  ConnectedService, 
  PlaybackPreferences,
  MusicService,
  AppError 
} from '../types';
import LocationService from '../services/LocationService';
import DatabaseService from '../services/DatabaseService';
import SpotifyService from '../services/SpotifyService';

// State Interface
interface AppState {
  // User & Authentication
  user: User | null;
  isAuthenticated: boolean;
  connectedServices: ConnectedService[];

  // Location
  currentLocation: Location | null;
  isLocationTracking: boolean;
  locationPermissions: {
    foreground: boolean;
    background: boolean;
  };

  // Location Tags
  userTags: LocationTag[];
  publicTags: LocationTag[];
  activeGeofences: LocationTag[];

  // Music & Playback
  currentTrack: Track | null;
  isPlaying: boolean;
  playbackQueue: Track[];
  playbackPreferences: PlaybackPreferences;

  // UI State
  isLoading: boolean;
  error: AppError | null;
  selectedTag: LocationTag | null;
}

// Action Types
type AppAction =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_AUTHENTICATED'; payload: boolean }
  | { type: 'SET_CONNECTED_SERVICES'; payload: ConnectedService[] }
  | { type: 'ADD_CONNECTED_SERVICE'; payload: ConnectedService }
  | { type: 'REMOVE_CONNECTED_SERVICE'; payload: MusicService }
  | { type: 'SET_CURRENT_LOCATION'; payload: Location | null }
  | { type: 'SET_LOCATION_TRACKING'; payload: boolean }
  | { type: 'SET_LOCATION_PERMISSIONS'; payload: { foreground: boolean; background: boolean } }
  | { type: 'SET_USER_TAGS'; payload: LocationTag[] }
  | { type: 'ADD_USER_TAG'; payload: LocationTag }
  | { type: 'UPDATE_USER_TAG'; payload: LocationTag }
  | { type: 'REMOVE_USER_TAG'; payload: string }
  | { type: 'SET_PUBLIC_TAGS'; payload: LocationTag[] }
  | { type: 'SET_ACTIVE_GEOFENCES'; payload: LocationTag[] }
  | { type: 'SET_CURRENT_TRACK'; payload: Track | null }
  | { type: 'SET_IS_PLAYING'; payload: boolean }
  | { type: 'SET_PLAYBACK_QUEUE'; payload: Track[] }
  | { type: 'SET_PLAYBACK_PREFERENCES'; payload: PlaybackPreferences }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: AppError | null }
  | { type: 'SET_SELECTED_TAG'; payload: LocationTag | null };

// Initial State
const initialState: AppState = {
  user: null,
  isAuthenticated: false,
  connectedServices: [],
  currentLocation: null,
  isLocationTracking: false,
  locationPermissions: {
    foreground: false,
    background: false,
  },
  userTags: [],
  publicTags: [],
  activeGeofences: [],
  currentTrack: null,
  isPlaying: false,
  playbackQueue: [],
  playbackPreferences: {
    autoPlayOnLocationEnter: true,
    fadeInDuration: 2000,
    fadeOutDuration: 2000,
    overlapHandling: 'manual_selection' as any,
    proximityThreshold: 50,
    batteryOptimization: true,
  },
  isLoading: false,
  error: null,
  selectedTag: null,
};

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload };
    
    case 'SET_AUTHENTICATED':
      return { ...state, isAuthenticated: action.payload };
    
    case 'SET_CONNECTED_SERVICES':
      return { ...state, connectedServices: action.payload };
    
    case 'ADD_CONNECTED_SERVICE':
      return {
        ...state,
        connectedServices: [...state.connectedServices.filter(s => s.service !== action.payload.service), action.payload],
      };
    
    case 'REMOVE_CONNECTED_SERVICE':
      return {
        ...state,
        connectedServices: state.connectedServices.filter(s => s.service !== action.payload),
      };
    
    case 'SET_CURRENT_LOCATION':
      return { ...state, currentLocation: action.payload };
    
    case 'SET_LOCATION_TRACKING':
      return { ...state, isLocationTracking: action.payload };
    
    case 'SET_LOCATION_PERMISSIONS':
      return { ...state, locationPermissions: action.payload };
    
    case 'SET_USER_TAGS':
      return { ...state, userTags: action.payload };
    
    case 'ADD_USER_TAG':
      return { ...state, userTags: [...state.userTags, action.payload] };
    
    case 'UPDATE_USER_TAG':
      return {
        ...state,
        userTags: state.userTags.map(tag => 
          tag.id === action.payload.id ? action.payload : tag
        ),
      };
    
    case 'REMOVE_USER_TAG':
      return {
        ...state,
        userTags: state.userTags.filter(tag => tag.id !== action.payload),
      };
    
    case 'SET_PUBLIC_TAGS':
      return { ...state, publicTags: action.payload };
    
    case 'SET_ACTIVE_GEOFENCES':
      return { ...state, activeGeofences: action.payload };
    
    case 'SET_CURRENT_TRACK':
      return { ...state, currentTrack: action.payload };
    
    case 'SET_IS_PLAYING':
      return { ...state, isPlaying: action.payload };
    
    case 'SET_PLAYBACK_QUEUE':
      return { ...state, playbackQueue: action.payload };
    
    case 'SET_PLAYBACK_PREFERENCES':
      return { ...state, playbackPreferences: action.payload };
    
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'SET_SELECTED_TAG':
      return { ...state, selectedTag: action.payload };
    
    default:
      return state;
  }
}

// Context
const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  actions: {
    // Authentication
    initializeApp: () => Promise<void>;
    connectMusicService: (service: MusicService) => Promise<void>;
    disconnectMusicService: (service: MusicService) => Promise<void>;
    
    // Location
    startLocationTracking: () => Promise<void>;
    stopLocationTracking: () => Promise<void>;
    getCurrentLocation: () => Promise<Location | null>;
    
    // Tags
    createLocationTag: (tag: Omit<LocationTag, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
    updateLocationTag: (tag: LocationTag) => Promise<void>;
    deleteLocationTag: (tagId: string) => Promise<void>;
    loadUserTags: () => Promise<void>;
    loadPublicTags: () => Promise<void>;
    
    // Playback
    playTrack: (track: Track) => Promise<void>;
    pausePlayback: () => Promise<void>;
    resumePlayback: () => Promise<void>;
    skipToNext: () => Promise<void>;
    skipToPrevious: () => Promise<void>;
    
    // Utility
    clearError: () => void;
  };
} | null>(null);

// Provider Component
interface AppProviderProps {
  children: ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Initialize app
  const initializeApp = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Initialize database
      await DatabaseService.initialize();
      
      // Check location permissions
      const permissions = await LocationService.checkPermissions();
      dispatch({ type: 'SET_LOCATION_PERMISSIONS', payload: permissions });
      
      // Load stored music service tokens
      const spotifyConnected = await SpotifyService.loadStoredTokens();
      if (spotifyConnected) {
        // You would get the user info and create ConnectedService object
        // This is simplified for now
      }
      
      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      console.error('App initialization error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Music service connection
  const connectMusicService = async (service: MusicService) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      let connectedService: ConnectedService;
      
      switch (service) {
        case MusicService.SPOTIFY:
          connectedService = await SpotifyService.authenticate();
          break;
        default:
          throw new Error(`Service ${service} not implemented yet`);
      }
      
      // Save to database
      await DatabaseService.saveConnectedService(connectedService);
      
      dispatch({ type: 'ADD_CONNECTED_SERVICE', payload: connectedService });
      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      console.error('Music service connection error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const disconnectMusicService = async (service: MusicService) => {
    try {
      switch (service) {
        case MusicService.SPOTIFY:
          await SpotifyService.disconnect();
          break;
        default:
          break;
      }
      
      dispatch({ type: 'REMOVE_CONNECTED_SERVICE', payload: service });
    } catch (error) {
      console.error('Music service disconnection error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
    }
  };

  // Location methods
  const startLocationTracking = async () => {
    try {
      await LocationService.startLocationTracking();
      dispatch({ type: 'SET_LOCATION_TRACKING', payload: true });
      
      // Set up location update listener
      LocationService.onLocationUpdate((location) => {
        dispatch({ type: 'SET_CURRENT_LOCATION', payload: location });
      });
    } catch (error) {
      console.error('Location tracking error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
    }
  };

  const stopLocationTracking = async () => {
    try {
      await LocationService.stopLocationTracking();
      dispatch({ type: 'SET_LOCATION_TRACKING', payload: false });
    } catch (error) {
      console.error('Stop location tracking error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
    }
  };

  const getCurrentLocation = async (): Promise<Location | null> => {
    try {
      const location = await LocationService.getOneTimeLocation();
      dispatch({ type: 'SET_CURRENT_LOCATION', payload: location });
      return location;
    } catch (error) {
      console.error('Get current location error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
      return null;
    }
  };

  // Tag methods (simplified - you'd implement full CRUD operations)
  const createLocationTag = async (tagData: Omit<LocationTag, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const tag: LocationTag = {
        ...tagData,
        id: `tag_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };
      
      await DatabaseService.saveLocationTag(tag);
      dispatch({ type: 'ADD_USER_TAG', payload: tag });
    } catch (error) {
      console.error('Create location tag error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
    }
  };

  const updateLocationTag = async (tag: LocationTag) => {
    try {
      const updatedTag = { ...tag, updatedAt: Date.now() };
      await DatabaseService.saveLocationTag(updatedTag);
      dispatch({ type: 'UPDATE_USER_TAG', payload: updatedTag });
    } catch (error) {
      console.error('Update location tag error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
    }
  };

  const deleteLocationTag = async (tagId: string) => {
    try {
      await DatabaseService.deleteLocationTag(tagId);
      dispatch({ type: 'REMOVE_USER_TAG', payload: tagId });
    } catch (error) {
      console.error('Delete location tag error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
    }
  };

  const loadUserTags = async () => {
    try {
      if (state.user) {
        const tags = await DatabaseService.getUserLocationTags(state.user.id);
        dispatch({ type: 'SET_USER_TAGS', payload: tags });
      }
    } catch (error) {
      console.error('Load user tags error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
    }
  };

  const loadPublicTags = async () => {
    try {
      const tags = await DatabaseService.getPublicLocationTags();
      dispatch({ type: 'SET_PUBLIC_TAGS', payload: tags });
    } catch (error) {
      console.error('Load public tags error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
    }
  };

  // Playback methods (simplified)
  const playTrack = async (track: Track) => {
    try {
      switch (track.service) {
        case MusicService.SPOTIFY:
          await SpotifyService.playTrack(track.serviceTrackId);
          break;
        default:
          throw new Error(`Playback for ${track.service} not implemented`);
      }
      
      dispatch({ type: 'SET_CURRENT_TRACK', payload: track });
      dispatch({ type: 'SET_IS_PLAYING', payload: true });
    } catch (error) {
      console.error('Play track error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
    }
  };

  const pausePlayback = async () => {
    try {
      // Determine which service is currently playing
      if (state.currentTrack) {
        switch (state.currentTrack.service) {
          case MusicService.SPOTIFY:
            await SpotifyService.pausePlayback();
            break;
        }
      }
      
      dispatch({ type: 'SET_IS_PLAYING', payload: false });
    } catch (error) {
      console.error('Pause playback error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
    }
  };

  const resumePlayback = async () => {
    try {
      if (state.currentTrack) {
        switch (state.currentTrack.service) {
          case MusicService.SPOTIFY:
            await SpotifyService.resumePlayback();
            break;
        }
      }
      
      dispatch({ type: 'SET_IS_PLAYING', payload: true });
    } catch (error) {
      console.error('Resume playback error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
    }
  };

  const skipToNext = async () => {
    try {
      if (state.currentTrack) {
        switch (state.currentTrack.service) {
          case MusicService.SPOTIFY:
            await SpotifyService.skipToNext();
            break;
        }
      }
    } catch (error) {
      console.error('Skip to next error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
    }
  };

  const skipToPrevious = async () => {
    try {
      if (state.currentTrack) {
        switch (state.currentTrack.service) {
          case MusicService.SPOTIFY:
            await SpotifyService.skipToPrevious();
            break;
        }
      }
    } catch (error) {
      console.error('Skip to previous error:', error);
      dispatch({ type: 'SET_ERROR', payload: error as AppError });
    }
  };

  const clearError = () => {
    dispatch({ type: 'SET_ERROR', payload: null });
  };

  // Initialize app on mount
  useEffect(() => {
    initializeApp();
  }, []);

  const actions = {
    initializeApp,
    connectMusicService,
    disconnectMusicService,
    startLocationTracking,
    stopLocationTracking,
    getCurrentLocation,
    createLocationTag,
    updateLocationTag,
    deleteLocationTag,
    loadUserTags,
    loadPublicTags,
    playTrack,
    pausePlayback,
    resumePlayback,
    skipToNext,
    skipToPrevious,
    clearError,
  };

  return (
    <AppContext.Provider value={{ state, dispatch, actions }}>
      {children}
    </AppContext.Provider>
  );
}

// Hook to use the context
export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}

export default AppContext;
