{"version": 3, "sources": ["hoverGesture.ts"], "names": ["ContinousBaseGesture", "HoverEffect", "hoverGestureHandlerProps", "changeEventCalculator", "current", "previous", "changePayload", "undefined", "changeX", "x", "changeY", "y", "HoverGesture", "constructor", "handler<PERSON>ame", "effect", "config", "hoverEffect", "onChange", "callback", "handlers"], "mappings": ";;AAAA,SAA4BA,oBAA5B,QAAwD,WAAxD;AASA,WAAYC,WAAZ;;WAAYA,W;AAAAA,EAAAA,W,CAAAA,W;AAAAA,EAAAA,W,CAAAA,W;AAAAA,EAAAA,W,CAAAA,W;GAAAA,W,KAAAA,W;;AAUZ,OAAO,MAAMC,wBAAwB,GAAG,CAAC,aAAD,CAAjC;;AAEP,SAASC,qBAAT,CACEC,OADF,EAEEC,QAFF,EAGE;AACA;;AACA,MAAIC,aAAJ;;AACA,MAAID,QAAQ,KAAKE,SAAjB,EAA4B;AAC1BD,IAAAA,aAAa,GAAG;AACdE,MAAAA,OAAO,EAAEJ,OAAO,CAACK,CADH;AAEdC,MAAAA,OAAO,EAAEN,OAAO,CAACO;AAFH,KAAhB;AAID,GALD,MAKO;AACLL,IAAAA,aAAa,GAAG;AACdE,MAAAA,OAAO,EAAEJ,OAAO,CAACK,CAAR,GAAYJ,QAAQ,CAACI,CADhB;AAEdC,MAAAA,OAAO,EAAEN,OAAO,CAACO,CAAR,GAAYN,QAAQ,CAACM;AAFhB,KAAhB;AAID;;AAED,SAAO,EAAE,GAAGP,OAAL;AAAc,OAAGE;AAAjB,GAAP;AACD;;AAED,OAAO,MAAMM,YAAN,SAA2BZ,oBAA3B,CAGL;AAGAa,EAAAA,WAAW,GAAG;AACZ;;AADY,oCAF0C,EAE1C;;AAGZ,SAAKC,WAAL,GAAmB,qBAAnB;AACD;AAED;AACF;AACA;AACA;;;AACEC,EAAAA,MAAM,CAACA,MAAD,EAAsB;AAC1B,SAAKC,MAAL,CAAYC,WAAZ,GAA0BF,MAA1B;AACA,WAAO,IAAP;AACD;;AAEDG,EAAAA,QAAQ,CACNC,QADM,EAMN;AACA;AACA,SAAKC,QAAL,CAAcjB,qBAAd,GAAsCA,qBAAtC;AACA,WAAO,MAAMe,QAAN,CAAeC,QAAf,CAAP;AACD;;AA5BD", "sourcesContent": ["import { BaseGestureConfig, ContinousBaseGesture } from './gesture';\nimport { GestureUpdateEvent } from '../gestureHandlerCommon';\nimport type { HoverGestureHandlerEventPayload } from '../GestureHandlerEventPayload';\n\nexport type HoverGestureChangeEventPayload = {\n  changeX: number;\n  changeY: number;\n};\n\nexport enum HoverEffect {\n  NONE = 0,\n  LIFT = 1,\n  HIGHLIGHT = 2,\n}\n\nexport interface HoverGestureConfig {\n  hoverEffect?: HoverEffect;\n}\n\nexport const hoverGestureHandlerProps = ['hoverEffect'] as const;\n\nfunction changeEventCalculator(\n  current: GestureUpdateEvent<HoverGestureHandlerEventPayload>,\n  previous?: GestureUpdateEvent<HoverGestureHandlerEventPayload>\n) {\n  'worklet';\n  let changePayload: HoverGestureChangeEventPayload;\n  if (previous === undefined) {\n    changePayload = {\n      changeX: current.x,\n      changeY: current.y,\n    };\n  } else {\n    changePayload = {\n      changeX: current.x - previous.x,\n      changeY: current.y - previous.y,\n    };\n  }\n\n  return { ...current, ...changePayload };\n}\n\nexport class HoverGesture extends ContinousBaseGesture<\n  HoverGestureHandlerEventPayload,\n  HoverGestureChangeEventPayload\n> {\n  public config: BaseGestureConfig & HoverGestureConfig = {};\n\n  constructor() {\n    super();\n\n    this.handlerName = 'HoverGestureHandler';\n  }\n\n  /**\n   * #### iOS only\n   * Sets the visual hover effect.\n   */\n  effect(effect: HoverEffect) {\n    this.config.hoverEffect = effect;\n    return this;\n  }\n\n  onChange(\n    callback: (\n      event: GestureUpdateEvent<\n        HoverGestureHandlerEventPayload & HoverGestureChangeEventPayload\n      >\n    ) => void\n  ) {\n    // @ts-ignore TS being overprotective, HoverGestureHandlerEventPayload is Record\n    this.handlers.changeEventCalculator = changeEventCalculator;\n    return super.onChange(callback);\n  }\n}\n\nexport type HoverGestureType = InstanceType<typeof HoverGesture>;\n"]}