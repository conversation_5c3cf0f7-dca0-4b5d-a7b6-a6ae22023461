{"version": 3, "sources": ["GestureHandlerButton.tsx"], "names": ["RNGestureHandlerButtonNativeComponent"], "mappings": "AAEA,OAAOA,qCAAP,MAAkD,gDAAlD;AAEA,eAAeA,qCAAf", "sourcesContent": ["import { HostComponent } from 'react-native';\nimport type { RawButtonProps } from './GestureButtonsProps';\nimport RNGestureHandlerButtonNativeComponent from '../specs/RNGestureHandlerButtonNativeComponent';\n\nexport default RNGestureHandlerButtonNativeComponent as HostComponent<RawButtonProps>;\n"]}