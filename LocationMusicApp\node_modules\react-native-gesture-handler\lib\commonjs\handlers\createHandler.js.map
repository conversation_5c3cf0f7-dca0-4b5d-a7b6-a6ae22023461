{"version": 3, "sources": ["createHandler.tsx"], "names": ["UIManagerAny", "UIManager", "customDirectEventTypes", "topGestureHandlerEvent", "registrationName", "customGHEventsConfigFabricAndroid", "topOnGestureHandlerEvent", "topOnGestureHandlerStateChange", "customGHEventsConfig", "onGestureHandlerEvent", "onGestureHandlerStateChange", "Platform", "OS", "genericDirectEventTypes", "UIManagerConstants", "getViewManagerConfig", "getConstants", "setJSResponder", "oldSetJSResponder", "clearJSResponder", "oldClearJSResponder", "tag", "blockNativeResponder", "RNGestureHandlerModule", "handleSetJSResponder", "handleClearJSResponder", "allowTouches", "DEV_ON_ANDROID", "__DEV__", "DeviceEventEmitter", "addListener", "hasUnresolvedRefs", "props", "extract", "refs", "Array", "isArray", "current", "some", "r", "stateToPropMappings", "State", "UNDETERMINED", "undefined", "BEGAN", "FAILED", "CANCELLED", "ACTIVE", "END", "UNRESOLVED_REFS_RETRY_LIMIT", "createHandler", "name", "allowedProps", "config", "transformProps", "customNativeProps", "Handler", "React", "Component", "constructor", "event", "nativeEvent", "handlerTag", "onGestureEvent", "onHandlerStateChange", "state", "stateEventName", "<PERSON><PERSON><PERSON><PERSON>", "node", "viewNode", "child", "Children", "only", "children", "ref", "newConfig", "createGestureHandler", "newViewTag", "viewTag", "attachGestureHandler", "ActionType", "JS_FUNCTION_OLD_API", "propsRef", "onGestureStateChange", "actionType", "isGestureHandlerWorklet", "isStateChangeHandlerWorklet", "is<PERSON><PERSON><PERSON>", "REANIMATED_WORKLET", "NATIVE_ANIMATED_EVENT", "MountRegistry", "gestureHandlerWillMount", "updateGestureHandler", "createRef", "isMountedRef", "id", "handlerIDToTag", "Error", "componentDidMount", "inspectorToggleListener", "setState", "_", "update", "displayName", "componentDidUpdate", "componentWillUnmount", "remove", "dropGestureHandler", "handlerID", "gestureHandlerWillUnmount", "remainingTries", "setNativeProps", "updates", "mergedProps", "render", "context", "gestureEventHandler", "gestureStateEventHandler", "events", "e", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "toArray", "push", "hitSlop", "cloneElement", "ref<PERSON><PERSON><PERSON>", "collapsable", "handlerType", "enabled", "testID", "GestureHandlerRootViewContext"], "mappings": ";;;;;;;AAAA;;AACA;;AAMA;;AACA;;AACA;;AACA;;AAKA;;AAOA;;AACA;;AAEA;;AAOA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;AAGA,MAAMA,YAAY,GAAGC,sBAArB;AAEAC,+CAAuBC,sBAAvB,GAAgD;AAC9CC,EAAAA,gBAAgB,EAAE;AAD4B,CAAhD;AAIA,MAAMC,iCAAiC,GAAG;AACxCC,EAAAA,wBAAwB,EAAE;AAAEF,IAAAA,gBAAgB,EAAE;AAApB,GADc;AAExCG,EAAAA,8BAA8B,EAAE;AAC9BH,IAAAA,gBAAgB,EAAE;AADY;AAFQ,CAA1C;AAOA,MAAMI,oBAAoB,GAAG;AAC3BC,EAAAA,qBAAqB,EAAE;AAAEL,IAAAA,gBAAgB,EAAE;AAApB,GADI;AAE3BM,EAAAA,2BAA2B,EAAE;AAC3BN,IAAAA,gBAAgB,EAAE;AADS,GAFF;AAM3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAI,2BACFO,sBAASC,EAAT,KAAgB,SADd,IAEFP,iCAFF;AAd2B,CAA7B,C,CAmBA;AACA;AACA;AACA;;AACAL,YAAY,CAACa,uBAAb,GAAuC,EACrC,GAAGb,YAAY,CAACa,uBADqB;AAErC,KAAGL;AAFkC,CAAvC,C,CAIA;AACA;AACA;;AACA,MAAMM,kBAAkB,sDACtBd,YAAY,CAACe,oBADS,2DACtB,4BAAAf,YAAY,EAAwB,cAAxB,CADU,kGAEtBA,YAAY,CAACgB,YAFS,0DAEtB,2BAAAhB,YAAY,CAFd;;AAIA,IAAIc,kBAAJ,EAAwB;AACtBA,EAAAA,kBAAkB,CAACD,uBAAnB,GAA6C,EAC3C,GAAGC,kBAAkB,CAACD,uBADqB;AAE3C,OAAGL;AAFwC,GAA7C;AAID,C,CAED;;;AACA,MAAM;AACJS,EAAAA,cAAc,EAAEC,iBAAiB,GAAG,MAAM,CACxC;AACD,GAHG;AAIJC,EAAAA,gBAAgB,EAAEC,mBAAmB,GAAG,MAAM,CAC5C;AACD;AANG,IAOFpB,YAPJ;;AAQAA,YAAY,CAACiB,cAAb,GAA8B,CAACI,GAAD,EAAcC,oBAAd,KAAgD;AAC5EC,kCAAuBC,oBAAvB,CAA4CH,GAA5C,EAAiDC,oBAAjD;;AACAJ,EAAAA,iBAAiB,CAACG,GAAD,EAAMC,oBAAN,CAAjB;AACD,CAHD;;AAIAtB,YAAY,CAACmB,gBAAb,GAAgC,MAAM;AACpCI,kCAAuBE,sBAAvB;;AACAL,EAAAA,mBAAmB;AACpB,CAHD;;AAKA,IAAIM,YAAY,GAAG,IAAnB;AACA,MAAMC,cAAc,GAAGC,OAAO,IAAIjB,sBAASC,EAAT,KAAgB,SAAlD,C,CACA;AACA;;AACA,IAAIe,cAAJ,EAAoB;AAClBE,kCAAmBC,WAAnB,CAA+B,wBAA/B,EAAyD,MAAM;AAC7DJ,IAAAA,YAAY,GAAG,CAACA,YAAhB;AACD,GAFD;AAGD;;AAKD,SAASK,iBAAT,CACEC,KADF,EAEE;AACA;AACA,QAAMC,OAAO,GAAIC,IAAD,IAAuB;AACrC,QAAI,CAACC,KAAK,CAACC,OAAN,CAAcF,IAAd,CAAL,EAA0B;AACxB,aAAOA,IAAI,IAAIA,IAAI,CAACG,OAAL,KAAiB,IAAhC;AACD;;AACD,WAAOH,IAAI,CAACI,IAAL,CAAWC,CAAD,IAAOA,CAAC,IAAIA,CAAC,CAACF,OAAF,KAAc,IAApC,CAAP;AACD,GALD;;AAMA,SAAOJ,OAAO,CAACD,KAAK,CAAC,sBAAD,CAAN,CAAP,IAA0CC,OAAO,CAACD,KAAK,CAAC,SAAD,CAAN,CAAxD;AACD;;AAED,MAAMQ,mBAAmB,GAAG;AAC1B,GAACC,aAAMC,YAAP,GAAsBC,SADI;AAE1B,GAACF,aAAMG,KAAP,GAAe,SAFW;AAG1B,GAACH,aAAMI,MAAP,GAAgB,UAHU;AAI1B,GAACJ,aAAMK,SAAP,GAAmB,aAJO;AAK1B,GAACL,aAAMM,MAAP,GAAgB,aALU;AAM1B,GAACN,aAAMO,GAAP,GAAa;AANa,CAA5B;AAgCA,MAAMC,2BAA2B,GAAG,CAApC,C,CAEA;;AACe,SAASC,aAAT,CAGb;AACAC,EAAAA,IADA;AAEAC,EAAAA,YAAY,GAAG,EAFf;AAGAC,EAAAA,MAAM,GAAG,EAHT;AAIAC,EAAAA,cAJA;AAKAC,EAAAA,iBAAiB,GAAG;AALpB,CAHa,EAS6D;AAI1E,QAAMC,OAAN,SAAsBC,KAAK,CAACC,SAA5B,CAGE;AAYAC,IAAAA,WAAW,CAAC3B,KAAD,EAAmC;AAC5C,YAAMA,KAAN;;AAD4C;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,qDAmFb4B,KAAD,IAA4B;AAC1D,YAAIA,KAAK,CAACC,WAAN,CAAkBC,UAAlB,KAAiC,KAAKA,UAA1C,EAAsD;AACpD,cAAI,OAAO,KAAK9B,KAAL,CAAW+B,cAAlB,KAAqC,UAAzC,EAAqD;AAAA;;AACnD,yDAAK/B,KAAL,EAAW+B,cAAX,kGAA4BH,KAA5B;AACD;AACF,SAJD,MAIO;AAAA;;AACL,yDAAK5B,KAAL,EAAWvB,qBAAX,qGAAmCmD,KAAnC;AACD;AACF,OA3F6C;;AAAA,2DA+F5CA,KADoC,IAEjC;AACH,YAAIA,KAAK,CAACC,WAAN,CAAkBC,UAAlB,KAAiC,KAAKA,UAA1C,EAAsD;AACpD,cAAI,OAAO,KAAK9B,KAAL,CAAWgC,oBAAlB,KAA2C,UAA/C,EAA2D;AAAA;;AACzD,0DAAKhC,KAAL,EAAWgC,oBAAX,mGAAkCJ,KAAlC;AACD;;AAED,gBAAMK,KAA4B,GAAGL,KAAK,CAACC,WAAN,CAAkBI,KAAvD;AACA,gBAAMC,cAAc,GAAG1B,mBAAmB,CAACyB,KAAD,CAA1C;AACA,gBAAME,YAAY,GAAGD,cAAc,IAAI,KAAKlC,KAAL,CAAWkC,cAAX,CAAvC;;AACA,cAAIC,YAAY,IAAI,OAAOA,YAAP,KAAwB,UAA5C,EAAwD;AACtDA,YAAAA,YAAY,CAACP,KAAD,CAAZ;AACD;AACF,SAXD,MAWO;AAAA;;AACL,yDAAK5B,KAAL,EAAWtB,2BAAX,qGAAyCkD,KAAzC;AACD;AACF,OA/G6C;;AAAA,0CAiHxBQ,IAAD,IAAe;AAAA;;AAClC,aAAKC,QAAL,GAAgBD,IAAhB;AAEA,cAAME,KAAK,GAAGb,KAAK,CAACc,QAAN,CAAeC,IAAf,CAAoB,KAAKxC,KAAL,CAAWyC,QAA/B,CAAd,CAHkC,CAIlC;AACA;;AACA,cAAMC,GAAG,GAAG,qCAAeJ,KAAD,CAAwBtC,KAAtC,2CAAc,OAA+B0C,GAA7C,GAAmDJ,KAAnD,aAAmDA,KAAnD,uBAAmDA,KAAK,CAAEI,GAAtE;;AAEA,YAAI,CAACA,GAAL,EAAU;AACR;AACD;;AAED,YAAI,OAAOA,GAAP,KAAe,UAAnB,EAA+B;AAC7BA,UAAAA,GAAG,CAACN,IAAD,CAAH;AACD,SAFD,MAEO;AACLM,UAAAA,GAAG,CAACrC,OAAJ,GAAc+B,IAAd;AACD;AACF,OAlI6C;;AAAA,oDAqI5CO,SAD6B,IAE1B;AACH,aAAKtB,MAAL,GAAcsB,SAAd;;AAEApD,wCAAuBqD,oBAAvB,CACEzB,IADF,EAEE,KAAKW,UAFP,EAGEa,SAHF;AAKD,OA9I6C;;AAAA,oDAgJdE,UAAD,IAAwB;AACrD,aAAKC,OAAL,GAAeD,UAAf;;AAEA,YAAIlE,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzB;AAEEW,0CAAuBwD,oBADzB,CAGE,KAAKjB,UAHP,EAIEe,UAJF,EAKEG,uBAAWC,mBALb,EAKkC;AAChC,eAAKC,QANP;AAQD,SAVD,MAUO;AACL,2DAA0B,KAAKpB,UAA/B,EAA2C;AACzCC,YAAAA,cAAc,EAAE,KAAKtD,qBADoB;AAEzC0E,YAAAA,oBAAoB,EAAE,KAAKzE;AAFc,WAA3C;;AAKA,gBAAM0E,UAAU,GAAG,CAAC,MAAM;AAAA;;AACxB,kBAAMrB,cAAc,mBAAG,KAAK/B,KAAR,iDAAG,aAAY+B,cAAnC;AACA,kBAAMsB,uBAAuB,GAC3BtB,cAAc,KACb,aAAaA,cAAb,IACC,yBAAyBA,cAFb,CADhB;AAIA,kBAAMC,oBAAoB,mBAAG,KAAKhC,KAAR,iDAAG,aAAYgC,oBAAzC;AACA,kBAAMsB,2BAA2B,GAC/BtB,oBAAoB,KACnB,aAAaA,oBAAb,IACC,yBAAyBA,oBAFP,CADtB;AAIA,kBAAMuB,mBAAmB,GACvBF,uBAAuB,IAAIC,2BAD7B;;AAEA,gBAAIC,mBAAJ,EAAyB;AACvB;AACA,qBAAOP,uBAAWQ,kBAAlB;AACD,aAHD,MAGO,IAAIzB,cAAc,IAAI,gBAAgBA,cAAtC,EAAsD;AAC3D;AACA,qBAAOiB,uBAAWS,qBAAlB;AACD,aAHM,MAGA;AACL;AACA,qBAAOT,uBAAWC,mBAAlB;AACD;AACF,WAvBkB,GAAnB;;AAyBA1D,0CAAuBwD,oBAAvB,CACE,KAAKjB,UADP,EAEEe,UAFF,EAGEO,UAHF;AAKD;;AAED;AAEA,gDAAiB,MAAM;AACrBM,uCAAcC,uBAAd,CAAsC,IAAtC;AACD,SAFD;AAGD,OAxM6C;;AAAA,oDA2M5ChB,SAD6B,IAE1B;AACH,aAAKtB,MAAL,GAAcsB,SAAd;;AAEApD,wCAAuBqE,oBAAvB,CAA4C,KAAK9B,UAAjD,EAA6Da,SAA7D;;AACA;AACD,OAjN6C;;AAE5C,WAAKb,UAAL,GAAkB,2CAAlB;AACA,WAAKT,MAAL,GAAc,EAAd;AACA,WAAK6B,QAAL,gBAAgBzB,KAAK,CAACoC,SAAN,EAAhB;AACA,WAAKC,YAAL,gBAAoBrC,KAAK,CAACoC,SAAN,EAApB;AACA,WAAK5B,KAAL,GAAa;AAAEvC,QAAAA;AAAF,OAAb;;AACA,UAAIM,KAAK,CAAC+D,EAAV,EAAc;AACZ,YAAIC,iCAAehE,KAAK,CAAC+D,EAArB,MAA6BpD,SAAjC,EAA4C;AAC1C,gBAAM,IAAIsD,KAAJ,CAAW,oBAAmBjE,KAAK,CAAC+D,EAAG,sBAAvC,CAAN;AACD;;AACDC,yCAAehE,KAAK,CAAC+D,EAArB,IAA2B,KAAKjC,UAAhC;AACD;AACF;;AAEDoC,IAAAA,iBAAiB,GAAG;AAClB,YAAMlE,KAAsB,GAAG,KAAKA,KAApC;AACA,WAAK8D,YAAL,CAAkBzD,OAAlB,GAA4B,IAA5B;;AAEA,UAAIV,cAAJ,EAAoB;AAClB,aAAKwE,uBAAL,GAA+BtE,gCAAmBC,WAAnB,CAC7B,wBAD6B,EAE7B,MAAM;AACJ,eAAKsE,QAAL,CAAeC,CAAD,KAAQ;AAAE3E,YAAAA;AAAF,WAAR,CAAd;AACA,eAAK4E,MAAL,CAAYrD,2BAAZ;AACD,SAL4B,CAA/B;AAOD;;AACD,UAAIlB,iBAAiB,CAACC,KAAD,CAArB,EAA8B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,gDAAiB,MAAM;AACrB,eAAKsE,MAAL,CAAYrD,2BAAZ;AACD,SAFD;AAGD;;AAED,WAAK2B,oBAAL,CACE,yBACEtB,cAAc,GAAGA,cAAc,CAAC,KAAKtB,KAAN,CAAjB,GAAgC,KAAKA,KADrD,EAEE,CAAC,GAAGoB,YAAJ,EAAkB,GAAGG,iBAArB,CAFF,EAGEF,MAHF,CADF;;AAQA,UAAI,CAAC,KAAKgB,QAAV,EAAoB;AAClB,cAAM,IAAI4B,KAAJ,CACH,+CAA8CzC,OAAO,CAAC+C,WAAY,4DAD/D,CAAN;AAGD;;AAED,WAAKxB,oBAAL,CAA0B,6BAAe,KAAKV,QAApB,CAA1B,EAvCkB,CAuCkD;AACrE;;AAEDmC,IAAAA,kBAAkB,GAAG;AACnB,YAAM1B,OAAO,GAAG,6BAAe,KAAKT,QAApB,CAAhB;;AACA,UAAI,KAAKS,OAAL,KAAiBA,OAArB,EAA8B;AAC5B,aAAKC,oBAAL,CAA0BD,OAA1B,EAD4B,CACkB;AAC/C;;AACD,WAAKwB,MAAL,CAAYrD,2BAAZ;AACD;;AAEDwD,IAAAA,oBAAoB,GAAG;AAAA;;AACrB,oCAAKN,uBAAL,gFAA8BO,MAA9B;AACA,WAAKZ,YAAL,CAAkBzD,OAAlB,GAA4B,KAA5B;;AACA,UAAI1B,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzB,2DAA4B,KAAKkD,UAAjC;AACD;;AACDvC,sCAAuBoF,kBAAvB,CAA0C,KAAK7C,UAA/C;;AACA,4CAPqB,CAQrB;;AACA,YAAM8C,SAA6B,GAAG,KAAK5E,KAAL,CAAW+D,EAAjD;;AACA,UAAIa,SAAJ,EAAe;AACb;AACA,eAAOZ,iCAAeY,SAAf,CAAP;AACD;;AAEDlB,mCAAcmB,yBAAd,CAAwC,IAAxC;AACD;;AAkIOP,IAAAA,MAAM,CAACQ,cAAD,EAAyB;AACrC,UAAI,CAAC,KAAKhB,YAAL,CAAkBzD,OAAvB,EAAgC;AAC9B;AACD;;AAED,YAAML,KAAsB,GAAG,KAAKA,KAApC,CALqC,CAOrC;AACA;AACA;;AACA,UAAID,iBAAiB,CAACC,KAAD,CAAjB,IAA4B8E,cAAc,GAAG,CAAjD,EAAoD;AAClD,gDAAiB,MAAM;AACrB,eAAKR,MAAL,CAAYQ,cAAc,GAAG,CAA7B;AACD,SAFD;AAGD,OAJD,MAIO;AACL,cAAMnC,SAAS,GAAG,yBAChBrB,cAAc,GAAGA,cAAc,CAAC,KAAKtB,KAAN,CAAjB,GAAgC,KAAKA,KADnC,EAEhB,CAAC,GAAGoB,YAAJ,EAAkB,GAAGG,iBAArB,CAFgB,EAGhBF,MAHgB,CAAlB;;AAKA,YAAI,CAAC,uBAAU,KAAKA,MAAf,EAAuBsB,SAAvB,CAAL,EAAwC;AACtC,eAAKiB,oBAAL,CAA0BjB,SAA1B;AACD;AACF;AACF;;AAEDoC,IAAAA,cAAc,CAACC,OAAD,EAAe;AAC3B,YAAMC,WAAW,GAAG,EAAE,GAAG,KAAKjF,KAAV;AAAiB,WAAGgF;AAApB,OAApB;AACA,YAAMrC,SAAS,GAAG,yBAChBrB,cAAc,GAAGA,cAAc,CAAC2D,WAAD,CAAjB,GAAiCA,WAD/B,EAEhB,CAAC,GAAG7D,YAAJ,EAAkB,GAAGG,iBAArB,CAFgB,EAGhBF,MAHgB,CAAlB;AAKA,WAAKuC,oBAAL,CAA0BjB,SAA1B;AACD;;AAEDuC,IAAAA,MAAM,GAAG;AAAA;;AACP,UAAItF,OAAO,IAAI,CAAC,KAAKuF,OAAjB,IAA4B,CAAC,wBAA7B,IAA4CxG,sBAASC,EAAT,KAAgB,KAAhE,EAAuE;AACrE,cAAM,IAAIqF,KAAJ,CACJ9C,IAAI,GACF,yMAFE,CAAN;AAID;;AAED,UAAIiE,mBAAmB,GAAG,KAAK3G,qBAA/B,CARO,CASP;;AAKA,YAAM;AAAEsD,QAAAA,cAAF;AAAkBtD,QAAAA;AAAlB,UACJ,KAAKuB,KADP;;AAEA,UAAI+B,cAAc,IAAI,OAAOA,cAAP,KAA0B,UAAhD,EAA4D;AAC1D;AACA;AACA;AACA,YAAItD,qBAAJ,EAA2B;AACzB,gBAAM,IAAIwF,KAAJ,CACJ,yEADI,CAAN;AAGD;;AACDmB,QAAAA,mBAAmB,GAAGrD,cAAtB;AACD,OAVD,MAUO;AACL,YACEtD,qBAAqB,IACrB,OAAOA,qBAAP,KAAiC,UAFnC,EAGE;AACA,gBAAM,IAAIwF,KAAJ,CACJ,yEADI,CAAN;AAGD;AACF;;AAED,UAAIoB,wBAAwB,GAAG,KAAK3G,2BAApC,CArCO,CAsCP;;AAKA,YAAM;AACJsD,QAAAA,oBADI;AAEJtD,QAAAA;AAFI,UAG4B,KAAKsB,KAHvC;;AAIA,UAAIgC,oBAAoB,IAAI,OAAOA,oBAAP,KAAgC,UAA5D,EAAwE;AACtE;AACA;AACA;AACA,YAAItD,2BAAJ,EAAiC;AAC/B,gBAAM,IAAIuF,KAAJ,CACJ,yEADI,CAAN;AAGD;;AACDoB,QAAAA,wBAAwB,GAAGrD,oBAA3B;AACD,OAVD,MAUO;AACL,YACEtD,2BAA2B,IAC3B,OAAOA,2BAAP,KAAuC,UAFzC,EAGE;AACA,gBAAM,IAAIuF,KAAJ,CACJ,yEADI,CAAN;AAGD;AACF;;AACD,YAAMqB,MAAM,GAAG;AACb7G,QAAAA,qBAAqB,EAAE,KAAKwD,KAAL,CAAWvC,YAAX,GACnB0F,mBADmB,GAEnBzE,SAHS;AAIbjC,QAAAA,2BAA2B,EAAE,KAAKuD,KAAL,CAAWvC,YAAX,GACzB2F,wBADyB,GAEzB1E;AANS,OAAf;AASA,WAAKuC,QAAL,CAAc7C,OAAd,GAAwBiF,MAAxB;AAEA,UAAIhD,KAAU,GAAG,IAAjB;;AACA,UAAI;AACFA,QAAAA,KAAK,GAAGb,KAAK,CAACc,QAAN,CAAeC,IAAf,CAAoB,KAAKxC,KAAL,CAAWyC,QAA/B,CAAR;AACD,OAFD,CAEE,OAAO8C,CAAP,EAAU;AACV,cAAM,IAAItB,KAAJ,CACJ,wBACG,GAAE9C,IAAK,4JADV,CADI,CAAN;AAKD;;AAED,UAAIqE,aAAa,GAAGlD,KAAK,CAACtC,KAAN,CAAYyC,QAAhC;;AACA,UACE7C,OAAO,IACP0C,KAAK,CAACmD,IADN,KAECnD,KAAK,CAACmD,IAAN,KAAe,wBAAf,IACCnD,KAAK,CAACmD,IAAN,CAAWtE,IAAX,KAAoB,MADrB,IAECmB,KAAK,CAACmD,IAAN,CAAWlB,WAAX,KAA2B,MAJ7B,CADF,EAME;AACAiB,QAAAA,aAAa,GAAG/D,KAAK,CAACc,QAAN,CAAemD,OAAf,CAAuBF,aAAvB,CAAhB;AACAA,QAAAA,aAAa,CAACG,IAAd,eACE,oBAAC,4CAAD;AACE,UAAA,GAAG,EAAC,uBADN;AAEE,UAAA,KAAK,EAAC,mBAFR;AAGE,UAAA,OAAO,EAAErD,KAAK,CAACtC,KAAN,CAAY4F;AAHvB,UADF;AAOD;;AAED,0BAAOnE,KAAK,CAACoE,YAAN,CACLvD,KADK,EAEL;AACEI,QAAAA,GAAG,EAAE,KAAKoD,UADZ;AAEEC,QAAAA,WAAW,EAAE,KAFf;AAGE,YAAI,2BACA;AACEC,UAAAA,WAAW,EAAE7E,IADf;AAEEW,UAAAA,UAAU,EAAE,KAAKA,UAFnB;AAGEmE,UAAAA,OAAO,EAAE,KAAKjG,KAAL,CAAWiG;AAHtB,SADA,GAMA,EANJ,CAHF;AAUEC,QAAAA,MAAM,wBAAE,KAAKlG,KAAL,CAAWkG,MAAb,mEAAuB5D,KAAK,CAACtC,KAAN,CAAYkG,MAV3C;AAWE,WAAGZ;AAXL,OAFK,EAeLE,aAfK,CAAP;AAiBD;;AA/XD;;AAPwE,kBAIpEhE,OAJoE,iBAQnDL,IARmD;;AAAA,kBAIpEK,OAJoE,iBASnD2E,sCATmD;;AAwY1E,SAAO3E,OAAP;AACD", "sourcesContent": ["import * as React from 'react';\nimport {\n  Platform,\n  UIManager,\n  DeviceEventEmitter,\n  EmitterSubscription,\n} from 'react-native';\nimport { customDirectEventTypes } from './customDirectEventTypes';\nimport RNGestureHandlerModule from '../RNGestureHandlerModule';\nimport { State } from '../State';\nimport {\n  handlerIDToTag,\n  registerOldGestureHandler,\n  unregisterOldGestureHandler,\n} from './handlersRegistry';\nimport { getNextHandlerTag } from './getNextHandlerTag';\n\nimport {\n  BaseGestureHandlerProps,\n  GestureEvent,\n  HandlerStateChangeEvent,\n} from './gestureHandlerCommon';\nimport { filterConfig, scheduleFlushOperations } from './utils';\nimport findNodeHandle from '../findNodeHandle';\nimport { ValueOf } from '../typeUtils';\nimport {\n  deepEqual,\n  isFabric,\n  isReact19,\n  isTestEnv,\n  tagMessage,\n} from '../utils';\nimport { ActionType } from '../ActionType';\nimport { PressabilityDebugView } from './PressabilityDebugView';\nimport GestureHandlerRootViewContext from '../GestureHandlerRootViewContext';\nimport { ghQueueMicrotask } from '../ghQueueMicrotask';\nimport { MountRegistry } from '../mountRegistry';\nimport { ReactElement } from 'react';\n\nconst UIManagerAny = UIManager as any;\n\ncustomDirectEventTypes.topGestureHandlerEvent = {\n  registrationName: 'onGestureHandlerEvent',\n};\n\nconst customGHEventsConfigFabricAndroid = {\n  topOnGestureHandlerEvent: { registrationName: 'onGestureHandlerEvent' },\n  topOnGestureHandlerStateChange: {\n    registrationName: 'onGestureHandlerStateChange',\n  },\n};\n\nconst customGHEventsConfig = {\n  onGestureHandlerEvent: { registrationName: 'onGestureHandlerEvent' },\n  onGestureHandlerStateChange: {\n    registrationName: 'onGestureHandlerStateChange',\n  },\n\n  // When using React Native Gesture Handler for Animated.event with useNativeDriver: true\n  // on Android with Fabric enabled, the native part still sends the native events to JS\n  // but prefixed with \"top\". We cannot simply rename the events above so they are prefixed\n  // with \"top\" instead of \"on\" because in such case Animated.events would not be registered.\n  // That's why we need to register another pair of event names.\n  // The incoming events will be queued but never handled.\n  // Without this piece of code below, you'll get the following JS error:\n  // Unsupported top level event type \"topOnGestureHandlerEvent\" dispatched\n  ...(isFabric() &&\n    Platform.OS === 'android' &&\n    customGHEventsConfigFabricAndroid),\n};\n\n// Add gesture specific events to genericDirectEventTypes object exported from UIManager\n// native module.\n// Once new event types are registered with react it is possible to dispatch these\n// events to all kind of native views.\nUIManagerAny.genericDirectEventTypes = {\n  ...UIManagerAny.genericDirectEventTypes,\n  ...customGHEventsConfig,\n};\n// In newer versions of RN the `genericDirectEventTypes` is located in the object\n// returned by UIManager.getViewManagerConfig('getConstants') or in older RN UIManager.getConstants(), we need to add it there as well to make\n// it compatible with RN 61+\nconst UIManagerConstants =\n  UIManagerAny.getViewManagerConfig?.('getConstants') ??\n  UIManagerAny.getConstants?.();\n\nif (UIManagerConstants) {\n  UIManagerConstants.genericDirectEventTypes = {\n    ...UIManagerConstants.genericDirectEventTypes,\n    ...customGHEventsConfig,\n  };\n}\n\n// Wrap JS responder calls and notify gesture handler manager\nconst {\n  setJSResponder: oldSetJSResponder = () => {\n    // no-op\n  },\n  clearJSResponder: oldClearJSResponder = () => {\n    // no-op\n  },\n} = UIManagerAny;\nUIManagerAny.setJSResponder = (tag: number, blockNativeResponder: boolean) => {\n  RNGestureHandlerModule.handleSetJSResponder(tag, blockNativeResponder);\n  oldSetJSResponder(tag, blockNativeResponder);\n};\nUIManagerAny.clearJSResponder = () => {\n  RNGestureHandlerModule.handleClearJSResponder();\n  oldClearJSResponder();\n};\n\nlet allowTouches = true;\nconst DEV_ON_ANDROID = __DEV__ && Platform.OS === 'android';\n// Toggled inspector blocks touch events in order to allow inspecting on Android\n// This needs to be a global variable in order to set initial state for `allowTouches` property in Handler component\nif (DEV_ON_ANDROID) {\n  DeviceEventEmitter.addListener('toggleElementInspector', () => {\n    allowTouches = !allowTouches;\n  });\n}\n\ntype HandlerProps<T extends Record<string, unknown>> = Readonly<\n  React.PropsWithChildren<BaseGestureHandlerProps<T>>\n>;\nfunction hasUnresolvedRefs<T extends Record<string, unknown>>(\n  props: HandlerProps<T>\n) {\n  // TODO(TS) - add type for extract arg\n  const extract = (refs: any | any[]) => {\n    if (!Array.isArray(refs)) {\n      return refs && refs.current === null;\n    }\n    return refs.some((r) => r && r.current === null);\n  };\n  return extract(props['simultaneousHandlers']) || extract(props['waitFor']);\n}\n\nconst stateToPropMappings = {\n  [State.UNDETERMINED]: undefined,\n  [State.BEGAN]: 'onBegan',\n  [State.FAILED]: 'onFailed',\n  [State.CANCELLED]: 'onCancelled',\n  [State.ACTIVE]: 'onActivated',\n  [State.END]: 'onEnded',\n} as const;\n\ntype CreateHandlerArgs<HandlerPropsT extends Record<string, unknown>> =\n  Readonly<{\n    name: string;\n    allowedProps: Readonly<Extract<keyof HandlerPropsT, string>[]>;\n    config: Readonly<Record<string, unknown>>;\n    transformProps?: (props: HandlerPropsT) => HandlerPropsT;\n    customNativeProps?: Readonly<string[]>;\n  }>;\n\n// TODO(TS) fix event types\ntype InternalEventHandlers = {\n  onGestureHandlerEvent?: (event: any) => void;\n  onGestureHandlerStateChange?: (event: any) => void;\n};\n\ntype AttachGestureHandlerWeb = (\n  handlerTag: number,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  newView: any,\n  _actionType: ActionType,\n  propsRef: React.RefObject<unknown>\n) => void;\n\nconst UNRESOLVED_REFS_RETRY_LIMIT = 1;\n\n// TODO(TS) - make sure that BaseGestureHandlerProps doesn't need other generic parameter to work with custom properties.\nexport default function createHandler<\n  T extends BaseGestureHandlerProps<U>,\n  U extends Record<string, unknown>,\n>({\n  name,\n  allowedProps = [],\n  config = {},\n  transformProps,\n  customNativeProps = [],\n}: CreateHandlerArgs<T>): React.ComponentType<T & React.RefAttributes<any>> {\n  interface HandlerState {\n    allowTouches: boolean;\n  }\n  class Handler extends React.Component<\n    T & InternalEventHandlers,\n    HandlerState\n  > {\n    static displayName = name;\n    static contextType = GestureHandlerRootViewContext;\n\n    private handlerTag: number;\n    private config: Record<string, unknown>;\n    private propsRef: React.MutableRefObject<unknown>;\n    private isMountedRef: React.MutableRefObject<boolean | null>;\n    private viewNode: any;\n    private viewTag?: number;\n    private inspectorToggleListener?: EmitterSubscription;\n\n    constructor(props: T & InternalEventHandlers) {\n      super(props);\n      this.handlerTag = getNextHandlerTag();\n      this.config = {};\n      this.propsRef = React.createRef();\n      this.isMountedRef = React.createRef();\n      this.state = { allowTouches };\n      if (props.id) {\n        if (handlerIDToTag[props.id] !== undefined) {\n          throw new Error(`Handler with ID \"${props.id}\" already registered`);\n        }\n        handlerIDToTag[props.id] = this.handlerTag;\n      }\n    }\n\n    componentDidMount() {\n      const props: HandlerProps<U> = this.props;\n      this.isMountedRef.current = true;\n\n      if (DEV_ON_ANDROID) {\n        this.inspectorToggleListener = DeviceEventEmitter.addListener(\n          'toggleElementInspector',\n          () => {\n            this.setState((_) => ({ allowTouches }));\n            this.update(UNRESOLVED_REFS_RETRY_LIMIT);\n          }\n        );\n      }\n      if (hasUnresolvedRefs(props)) {\n        // If there are unresolved refs (e.g. \".current\" has not yet been set)\n        // passed as `simultaneousHandlers` or `waitFor`, we enqueue a call to\n        // _update method that will try to update native handler props using\n        // queueMicrotask. This makes it so update() function gets called after all\n        // react components are mounted and we expect the missing ref object to\n        // be resolved by then.\n        ghQueueMicrotask(() => {\n          this.update(UNRESOLVED_REFS_RETRY_LIMIT);\n        });\n      }\n\n      this.createGestureHandler(\n        filterConfig(\n          transformProps ? transformProps(this.props) : this.props,\n          [...allowedProps, ...customNativeProps],\n          config\n        )\n      );\n\n      if (!this.viewNode) {\n        throw new Error(\n          `[Gesture Handler] Failed to obtain view for ${Handler.displayName}. Note that old API doesn't support functional components.`\n        );\n      }\n\n      this.attachGestureHandler(findNodeHandle(this.viewNode) as number); // TODO(TS) - check if this can be null\n    }\n\n    componentDidUpdate() {\n      const viewTag = findNodeHandle(this.viewNode);\n      if (this.viewTag !== viewTag) {\n        this.attachGestureHandler(viewTag as number); // TODO(TS) - check interaction between _viewTag & findNodeHandle\n      }\n      this.update(UNRESOLVED_REFS_RETRY_LIMIT);\n    }\n\n    componentWillUnmount() {\n      this.inspectorToggleListener?.remove();\n      this.isMountedRef.current = false;\n      if (Platform.OS !== 'web') {\n        unregisterOldGestureHandler(this.handlerTag);\n      }\n      RNGestureHandlerModule.dropGestureHandler(this.handlerTag);\n      scheduleFlushOperations();\n      // We can't use this.props.id directly due to TS generic type narrowing bug, see https://github.com/microsoft/TypeScript/issues/13995 for more context\n      const handlerID: string | undefined = this.props.id;\n      if (handlerID) {\n        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n        delete handlerIDToTag[handlerID];\n      }\n\n      MountRegistry.gestureHandlerWillUnmount(this);\n    }\n\n    private onGestureHandlerEvent = (event: GestureEvent<U>) => {\n      if (event.nativeEvent.handlerTag === this.handlerTag) {\n        if (typeof this.props.onGestureEvent === 'function') {\n          this.props.onGestureEvent?.(event);\n        }\n      } else {\n        this.props.onGestureHandlerEvent?.(event);\n      }\n    };\n\n    // TODO(TS) - make sure this is right type for event\n    private onGestureHandlerStateChange = (\n      event: HandlerStateChangeEvent<U>\n    ) => {\n      if (event.nativeEvent.handlerTag === this.handlerTag) {\n        if (typeof this.props.onHandlerStateChange === 'function') {\n          this.props.onHandlerStateChange?.(event);\n        }\n\n        const state: ValueOf<typeof State> = event.nativeEvent.state;\n        const stateEventName = stateToPropMappings[state];\n        const eventHandler = stateEventName && this.props[stateEventName];\n        if (eventHandler && typeof eventHandler === 'function') {\n          eventHandler(event);\n        }\n      } else {\n        this.props.onGestureHandlerStateChange?.(event);\n      }\n    };\n\n    private refHandler = (node: any) => {\n      this.viewNode = node;\n\n      const child = React.Children.only(this.props.children);\n      // @ts-ignore Since React 19 ref is accessible as standard prop\n      // https://react.dev/blog/2024/04/25/react-19-upgrade-guide#deprecated-element-ref\n      const ref = isReact19() ? (child as ReactElement).props?.ref : child?.ref;\n\n      if (!ref) {\n        return;\n      }\n\n      if (typeof ref === 'function') {\n        ref(node);\n      } else {\n        ref.current = node;\n      }\n    };\n\n    private createGestureHandler = (\n      newConfig: Readonly<Record<string, unknown>>\n    ) => {\n      this.config = newConfig;\n\n      RNGestureHandlerModule.createGestureHandler(\n        name,\n        this.handlerTag,\n        newConfig\n      );\n    };\n\n    private attachGestureHandler = (newViewTag: number) => {\n      this.viewTag = newViewTag;\n\n      if (Platform.OS === 'web') {\n        // Typecast due to dynamic resolution, attachGestureHandler should have web version signature in this branch\n        (\n          RNGestureHandlerModule.attachGestureHandler as AttachGestureHandlerWeb\n        )(\n          this.handlerTag,\n          newViewTag,\n          ActionType.JS_FUNCTION_OLD_API, // ignored on web\n          this.propsRef\n        );\n      } else {\n        registerOldGestureHandler(this.handlerTag, {\n          onGestureEvent: this.onGestureHandlerEvent,\n          onGestureStateChange: this.onGestureHandlerStateChange,\n        });\n\n        const actionType = (() => {\n          const onGestureEvent = this.props?.onGestureEvent;\n          const isGestureHandlerWorklet =\n            onGestureEvent &&\n            ('current' in onGestureEvent ||\n              'workletEventHandler' in onGestureEvent);\n          const onHandlerStateChange = this.props?.onHandlerStateChange;\n          const isStateChangeHandlerWorklet =\n            onHandlerStateChange &&\n            ('current' in onHandlerStateChange ||\n              'workletEventHandler' in onHandlerStateChange);\n          const isReanimatedHandler =\n            isGestureHandlerWorklet || isStateChangeHandlerWorklet;\n          if (isReanimatedHandler) {\n            // Reanimated worklet\n            return ActionType.REANIMATED_WORKLET;\n          } else if (onGestureEvent && '__isNative' in onGestureEvent) {\n            // Animated.event with useNativeDriver: true\n            return ActionType.NATIVE_ANIMATED_EVENT;\n          } else {\n            // JS callback or Animated.event with useNativeDriver: false\n            return ActionType.JS_FUNCTION_OLD_API;\n          }\n        })();\n\n        RNGestureHandlerModule.attachGestureHandler(\n          this.handlerTag,\n          newViewTag,\n          actionType\n        );\n      }\n\n      scheduleFlushOperations();\n\n      ghQueueMicrotask(() => {\n        MountRegistry.gestureHandlerWillMount(this);\n      });\n    };\n\n    private updateGestureHandler = (\n      newConfig: Readonly<Record<string, unknown>>\n    ) => {\n      this.config = newConfig;\n\n      RNGestureHandlerModule.updateGestureHandler(this.handlerTag, newConfig);\n      scheduleFlushOperations();\n    };\n\n    private update(remainingTries: number) {\n      if (!this.isMountedRef.current) {\n        return;\n      }\n\n      const props: HandlerProps<U> = this.props;\n\n      // When ref is set via a function i.e. `ref={(r) => refObject.current = r}` instead of\n      // `ref={refObject}` it's possible that it won't be resolved in time. Seems like trying\n      // again is easy enough fix.\n      if (hasUnresolvedRefs(props) && remainingTries > 0) {\n        ghQueueMicrotask(() => {\n          this.update(remainingTries - 1);\n        });\n      } else {\n        const newConfig = filterConfig(\n          transformProps ? transformProps(this.props) : this.props,\n          [...allowedProps, ...customNativeProps],\n          config\n        );\n        if (!deepEqual(this.config, newConfig)) {\n          this.updateGestureHandler(newConfig);\n        }\n      }\n    }\n\n    setNativeProps(updates: any) {\n      const mergedProps = { ...this.props, ...updates };\n      const newConfig = filterConfig(\n        transformProps ? transformProps(mergedProps) : mergedProps,\n        [...allowedProps, ...customNativeProps],\n        config\n      );\n      this.updateGestureHandler(newConfig);\n    }\n\n    render() {\n      if (__DEV__ && !this.context && !isTestEnv() && Platform.OS !== 'web') {\n        throw new Error(\n          name +\n            ' must be used as a descendant of GestureHandlerRootView. Otherwise the gestures will not be recognized. See https://docs.swmansion.com/react-native-gesture-handler/docs/installation for more details.'\n        );\n      }\n\n      let gestureEventHandler = this.onGestureHandlerEvent;\n      // Another instance of https://github.com/microsoft/TypeScript/issues/13995\n      type OnGestureEventHandlers = {\n        onGestureEvent?: BaseGestureHandlerProps<U>['onGestureEvent'];\n        onGestureHandlerEvent?: InternalEventHandlers['onGestureHandlerEvent'];\n      };\n      const { onGestureEvent, onGestureHandlerEvent }: OnGestureEventHandlers =\n        this.props;\n      if (onGestureEvent && typeof onGestureEvent !== 'function') {\n        // If it's not a method it should be an native Animated.event\n        // object. We set it directly as the handler for the view\n        // In this case nested handlers are not going to be supported\n        if (onGestureHandlerEvent) {\n          throw new Error(\n            'Nesting touch handlers with native animated driver is not supported yet'\n          );\n        }\n        gestureEventHandler = onGestureEvent;\n      } else {\n        if (\n          onGestureHandlerEvent &&\n          typeof onGestureHandlerEvent !== 'function'\n        ) {\n          throw new Error(\n            'Nesting touch handlers with native animated driver is not supported yet'\n          );\n        }\n      }\n\n      let gestureStateEventHandler = this.onGestureHandlerStateChange;\n      // Another instance of https://github.com/microsoft/TypeScript/issues/13995\n      type OnGestureStateChangeHandlers = {\n        onHandlerStateChange?: BaseGestureHandlerProps<U>['onHandlerStateChange'];\n        onGestureHandlerStateChange?: InternalEventHandlers['onGestureHandlerStateChange'];\n      };\n      const {\n        onHandlerStateChange,\n        onGestureHandlerStateChange,\n      }: OnGestureStateChangeHandlers = this.props;\n      if (onHandlerStateChange && typeof onHandlerStateChange !== 'function') {\n        // If it's not a method it should be an native Animated.event\n        // object. We set it directly as the handler for the view\n        // In this case nested handlers are not going to be supported\n        if (onGestureHandlerStateChange) {\n          throw new Error(\n            'Nesting touch handlers with native animated driver is not supported yet'\n          );\n        }\n        gestureStateEventHandler = onHandlerStateChange;\n      } else {\n        if (\n          onGestureHandlerStateChange &&\n          typeof onGestureHandlerStateChange !== 'function'\n        ) {\n          throw new Error(\n            'Nesting touch handlers with native animated driver is not supported yet'\n          );\n        }\n      }\n      const events = {\n        onGestureHandlerEvent: this.state.allowTouches\n          ? gestureEventHandler\n          : undefined,\n        onGestureHandlerStateChange: this.state.allowTouches\n          ? gestureStateEventHandler\n          : undefined,\n      };\n\n      this.propsRef.current = events;\n\n      let child: any = null;\n      try {\n        child = React.Children.only(this.props.children);\n      } catch (e) {\n        throw new Error(\n          tagMessage(\n            `${name} got more than one view as a child. If you want the gesture to work on multiple views, wrap them with a common parent and attach the gesture to that view.`\n          )\n        );\n      }\n\n      let grandChildren = child.props.children;\n      if (\n        __DEV__ &&\n        child.type &&\n        (child.type === 'RNGestureHandlerButton' ||\n          child.type.name === 'View' ||\n          child.type.displayName === 'View')\n      ) {\n        grandChildren = React.Children.toArray(grandChildren);\n        grandChildren.push(\n          <PressabilityDebugView\n            key=\"pressabilityDebugView\"\n            color=\"mediumspringgreen\"\n            hitSlop={child.props.hitSlop}\n          />\n        );\n      }\n\n      return React.cloneElement(\n        child,\n        {\n          ref: this.refHandler,\n          collapsable: false,\n          ...(isTestEnv()\n            ? {\n                handlerType: name,\n                handlerTag: this.handlerTag,\n                enabled: this.props.enabled,\n              }\n            : {}),\n          testID: this.props.testID ?? child.props.testID,\n          ...events,\n        },\n        grandChildren\n      );\n    }\n  }\n  return Handler;\n}\n"]}