{"version": 3, "sources": ["useViewRefHandler.ts"], "names": ["useViewRefHandler", "state", "updateAttachedGestures", "ref<PERSON><PERSON><PERSON>", "ref", "viewRef", "previousViewTag", "firstRender", "__DEV__", "global", "isViewFlatteningDisabled", "node", "console", "error"], "mappings": ";;;;;;;AAAA;;AACA;;AAGA;;AACA;;;;AAMA;AACA;AACA;AACO,SAASA,iBAAT,CACLC,KADK,EAELC,sBAFK,EAGL;AACA,QAAMC,UAAU,GAAG,wBAChBC,GAAD,IAAiC;AAC/B,QAAIA,GAAG,KAAK,IAAZ,EAAkB;AAChB;AACD;;AAEDH,IAAAA,KAAK,CAACI,OAAN,GAAgBD,GAAhB,CAL+B,CAO/B;;AACA,QAAIH,KAAK,CAACK,eAAN,KAA0B,CAAC,CAA/B,EAAkC;AAChCL,MAAAA,KAAK,CAACK,eAAN,GAAwB,6BAAeL,KAAK,CAACI,OAArB,CAAxB;AACD,KAV8B,CAY/B;AACA;;;AACA,QAAI,CAACJ,KAAK,CAACM,WAAX,EAAwB;AACtBL,MAAAA,sBAAsB,CAAC,IAAD,CAAtB;AACD;;AAED,QAAIM,OAAO,IAAI,sBAAX,IAAyBC,MAAM,CAACC,wBAApC,EAA8D;AAC5D,YAAMC,IAAI,GAAG,gDAAqBP,GAArB,CAAb;;AACA,UAAIK,MAAM,CAACC,wBAAP,CAAgCC,IAAhC,MAA0C,KAA9C,EAAqD;AACnDC,QAAAA,OAAO,CAACC,KAAR,CACE,uBACE,uEACE,kGAFJ,CADF;AAMD;AACF;AACF,GA9BgB,EA+BjB,CAACZ,KAAD,EAAQC,sBAAR,CA/BiB,CAAnB;AAkCA,SAAOC,UAAP;AACD", "sourcesContent": ["import { isFabric, tagMessage } from '../../../utils';\nimport { getShadowNodeFromRef } from '../../../getShadowNodeFromRef';\n\nimport { GestureDetectorState } from './types';\nimport React, { useCallback } from 'react';\nimport findNodeHandle from '../../../findNodeHandle';\n\ndeclare const global: {\n  isViewFlatteningDisabled: (node: unknown) => boolean | null; // JSI function\n};\n\n// Ref handler for the Wrap component attached under the GestureDetector.\n// It's responsible for setting the viewRef on the state and triggering the reattaching of handlers\n// if the view has changed.\nexport function useViewRefHandler(\n  state: GestureDetectorState,\n  updateAttachedGestures: (skipConfigUpdate?: boolean) => void\n) {\n  const refHandler = useCallback(\n    (ref: React.Component | null) => {\n      if (ref === null) {\n        return;\n      }\n\n      state.viewRef = ref;\n\n      // if it's the first render, also set the previousViewTag to prevent reattaching gestures when not needed\n      if (state.previousViewTag === -1) {\n        state.previousViewTag = findNodeHandle(state.viewRef) as number;\n      }\n\n      // Pass true as `skipConfigUpdate`. Here we only want to trigger the eventual reattaching of handlers\n      // in case the view has changed. If the view doesn't change, the update will be handled by detector.\n      if (!state.firstRender) {\n        updateAttachedGestures(true);\n      }\n\n      if (__DEV__ && isFabric() && global.isViewFlatteningDisabled) {\n        const node = getShadowNodeFromRef(ref);\n        if (global.isViewFlatteningDisabled(node) === false) {\n          console.error(\n            tagMessage(\n              'GestureDetector has received a child that may get view-flattened. ' +\n                '\\nTo prevent it from misbehaving you need to wrap the child with a `<View collapsable={false}>`.'\n            )\n          );\n        }\n      }\n    },\n    [state, updateAttachedGestures]\n  );\n\n  return refHandler;\n}\n"]}