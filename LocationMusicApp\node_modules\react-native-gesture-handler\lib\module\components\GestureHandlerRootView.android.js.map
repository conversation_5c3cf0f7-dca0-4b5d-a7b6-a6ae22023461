{"version": 3, "sources": ["GestureHandlerRootView.android.tsx"], "names": ["React", "StyleSheet", "maybeInitializeFabric", "GestureHandlerRootViewContext", "GestureHandlerRootViewNativeComponent", "GestureHandlerRootView", "style", "rest", "styles", "container", "create", "flex"], "mappings": ";;AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AAEA,SAAoBC,UAApB,QAAsC,cAAtC;AACA,SAASC,qBAAT,QAAsC,SAAtC;AACA,OAAOC,6BAAP,MAA0C,kCAA1C;AACA,OAAOC,qCAAP,MAAkD,kDAAlD;AAKA,eAAe,SAASC,sBAAT,CAAgC;AAC7CC,EAAAA,KAD6C;AAE7C,KAAGC;AAF0C,CAAhC,EAGiB;AAC9B;AACA;AACA;AACAL,EAAAA,qBAAqB;AAErB,sBACE,oBAAC,6BAAD,CAA+B,QAA/B;AAAwC,IAAA,KAAK;AAA7C,kBACE,oBAAC,qCAAD;AACE,IAAA,KAAK,EAAEI,KAAF,aAAEA,KAAF,cAAEA,KAAF,GAAWE,MAAM,CAACC;AADzB,KAEMF,IAFN,EADF,CADF;AAQD;AAED,MAAMC,MAAM,GAAGP,UAAU,CAACS,MAAX,CAAkB;AAC/BD,EAAAA,SAAS,EAAE;AAAEE,IAAAA,IAAI,EAAE;AAAR;AADoB,CAAlB,CAAf", "sourcesContent": ["import * as React from 'react';\nimport { PropsWithChildren } from 'react';\nimport { ViewProps, StyleSheet } from 'react-native';\nimport { maybeInitializeFabric } from '../init';\nimport GestureHandlerRootViewContext from '../GestureHandlerRootViewContext';\nimport GestureHandlerRootViewNativeComponent from '../specs/RNGestureHandlerRootViewNativeComponent';\n\nexport interface GestureHandlerRootViewProps\n  extends PropsWithChildren<ViewProps> {}\n\nexport default function GestureHandlerRootView({\n  style,\n  ...rest\n}: GestureHandlerRootViewProps) {\n  // Try initialize fabric on the first render, at this point we can\n  // reliably check if fabric is enabled (the function contains a flag\n  // to make sure it's called only once)\n  maybeInitializeFabric();\n\n  return (\n    <GestureHandlerRootViewContext.Provider value>\n      <GestureHandlerRootViewNativeComponent\n        style={style ?? styles.container}\n        {...rest}\n      />\n    </GestureHandlerRootViewContext.Provider>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: { flex: 1 },\n});\n"]}