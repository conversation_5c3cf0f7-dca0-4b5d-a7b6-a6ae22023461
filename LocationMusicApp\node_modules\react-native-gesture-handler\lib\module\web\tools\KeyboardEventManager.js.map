{"version": 3, "sources": ["KeyboardEventManager.ts"], "names": ["EventTypes", "EventManager", "PointerType", "KeyboardEventManager", "event", "cancelationKeys", "indexOf", "key", "isPressed", "dispatchEvent", "CANCEL", "activationKeys", "DOWN", "UP", "eventType", "target", "HTMLElement", "adaptedEvent", "mapEvent", "onPointerUp", "onPointerDown", "onPointerCancel", "registerListeners", "view", "addEventListener", "keyDownCallback", "keyUp<PERSON><PERSON><PERSON>", "unregisterListeners", "removeEventListener", "viewRect", "getBoundingClientRect", "viewportPosition", "x", "width", "y", "height", "relativePosition", "offsetX", "offsetY", "pointerId", "pointerType", "KEY", "time", "timeStamp"], "mappings": ";;AAAA,SAAuBA,UAAvB,QAAyC,eAAzC;AACA,OAAOC,YAAP,MAAyB,gBAAzB;AACA,SAASC,WAAT,QAA4B,mBAA5B;AAEA,eAAe,MAAMC,oBAAN,SAAmCF,YAAnC,CAA6D;AAAA;AAAA;;AAAA,4CACjD,CAAC,OAAD,EAAU,GAAV,CADiD;;AAAA,6CAEhD,CAAC,KAAD,CAFgD;;AAAA,uCAGtD,KAHsD;;AAAA,6CAK/CG,KAAD,IAAgC;AACxD,UAAI,KAAKC,eAAL,CAAqBC,OAArB,CAA6BF,KAAK,CAACG,GAAnC,MAA4C,CAAC,CAA7C,IAAkD,KAAKC,SAA3D,EAAsE;AACpE,aAAKC,aAAL,CAAmBL,KAAnB,EAA0BJ,UAAU,CAACU,MAArC;AACA;AACD;;AAED,UAAI,KAAKC,cAAL,CAAoBL,OAApB,CAA4BF,KAAK,CAACG,GAAlC,MAA2C,CAAC,CAAhD,EAAmD;AACjD;AACD;;AAED,WAAKE,aAAL,CAAmBL,KAAnB,EAA0BJ,UAAU,CAACY,IAArC;AACD,KAhByE;;AAAA,2CAkBjDR,KAAD,IAAgC;AACtD,UAAI,KAAKO,cAAL,CAAoBL,OAApB,CAA4BF,KAAK,CAACG,GAAlC,MAA2C,CAAC,CAA5C,IAAiD,CAAC,KAAKC,SAA3D,EAAsE;AACpE;AACD;;AAED,WAAKC,aAAL,CAAmBL,KAAnB,EAA0BJ,UAAU,CAACa,EAArC;AACD,KAxByE;AAAA;;AA0BlEJ,EAAAA,aAAa,CAACL,KAAD,EAAuBU,SAAvB,EAA8C;AACjE,QAAI,EAAEV,KAAK,CAACW,MAAN,YAAwBC,WAA1B,CAAJ,EAA4C;AAC1C;AACD;;AAED,UAAMC,YAAY,GAAG,KAAKC,QAAL,CAAcd,KAAd,EAAqBU,SAArB,CAArB;;AAEA,YAAQA,SAAR;AACE,WAAKd,UAAU,CAACa,EAAhB;AACE,aAAKL,SAAL,GAAiB,KAAjB;AACA,aAAKW,WAAL,CAAiBF,YAAjB;AACA;;AACF,WAAKjB,UAAU,CAACY,IAAhB;AACE,aAAKJ,SAAL,GAAiB,IAAjB;AACA,aAAKY,aAAL,CAAmBH,YAAnB;AACA;;AACF,WAAKjB,UAAU,CAACU,MAAhB;AACE,aAAKF,SAAL,GAAiB,KAAjB;AACA,aAAKa,eAAL,CAAqBJ,YAArB;AACA;AAZJ;AAcD;;AAEMK,EAAAA,iBAAiB,GAAS;AAC/B,SAAKC,IAAL,CAAUC,gBAAV,CAA2B,SAA3B,EAAsC,KAAKC,eAA3C;AACA,SAAKF,IAAL,CAAUC,gBAAV,CAA2B,OAA3B,EAAoC,KAAKE,aAAzC;AACD;;AAEMC,EAAAA,mBAAmB,GAAS;AACjC,SAAKJ,IAAL,CAAUK,mBAAV,CAA8B,SAA9B,EAAyC,KAAKH,eAA9C;AACA,SAAKF,IAAL,CAAUK,mBAAV,CAA8B,OAA9B,EAAuC,KAAKF,aAA5C;AACD;;AAESR,EAAAA,QAAQ,CAChBd,KADgB,EAEhBU,SAFgB,EAGF;AACd,UAAMe,QAAQ,GAAIzB,KAAK,CAACW,MAAP,CAA8Be,qBAA9B,EAAjB;AAEA,UAAMC,gBAAgB,GAAG;AACvBC,MAAAA,CAAC,EAAE,CAAAH,QAAQ,SAAR,IAAAA,QAAQ,WAAR,YAAAA,QAAQ,CAAEG,CAAV,IAAc,CAAAH,QAAQ,SAAR,IAAAA,QAAQ,WAAR,YAAAA,QAAQ,CAAEI,KAAV,IAAkB,CADZ;AAEvBC,MAAAA,CAAC,EAAE,CAAAL,QAAQ,SAAR,IAAAA,QAAQ,WAAR,YAAAA,QAAQ,CAAEK,CAAV,IAAc,CAAAL,QAAQ,SAAR,IAAAA,QAAQ,WAAR,YAAAA,QAAQ,CAAEM,MAAV,IAAmB;AAFb,KAAzB;AAKA,UAAMC,gBAAgB,GAAG;AACvBJ,MAAAA,CAAC,EAAE,CAAAH,QAAQ,SAAR,IAAAA,QAAQ,WAAR,YAAAA,QAAQ,CAAEI,KAAV,IAAkB,CADE;AAEvBC,MAAAA,CAAC,EAAE,CAAAL,QAAQ,SAAR,IAAAA,QAAQ,WAAR,YAAAA,QAAQ,CAAEM,MAAV,IAAmB;AAFC,KAAzB;AAKA,WAAO;AACLH,MAAAA,CAAC,EAAED,gBAAgB,CAACC,CADf;AAELE,MAAAA,CAAC,EAAEH,gBAAgB,CAACG,CAFf;AAGLG,MAAAA,OAAO,EAAED,gBAAgB,CAACJ,CAHrB;AAILM,MAAAA,OAAO,EAAEF,gBAAgB,CAACF,CAJrB;AAKLK,MAAAA,SAAS,EAAE,CALN;AAMLzB,MAAAA,SAAS,EAAEA,SANN;AAOL0B,MAAAA,WAAW,EAAEtC,WAAW,CAACuC,GAPpB;AAQLC,MAAAA,IAAI,EAAEtC,KAAK,CAACuC;AARP,KAAP;AAUD;;AArFyE", "sourcesContent": ["import { AdaptedEvent, EventTypes } from '../interfaces';\nimport EventManager from './EventManager';\nimport { PointerType } from '../../PointerType';\n\nexport default class KeyboardEventManager extends EventManager<HTMLElement> {\n  private activationKeys = ['Enter', ' '];\n  private cancelationKeys = ['Tab'];\n  private isPressed = false;\n\n  private keyDownCallback = (event: KeyboardEvent): void => {\n    if (this.cancelationKeys.indexOf(event.key) !== -1 && this.isPressed) {\n      this.dispatchEvent(event, EventTypes.CANCEL);\n      return;\n    }\n\n    if (this.activationKeys.indexOf(event.key) === -1) {\n      return;\n    }\n\n    this.dispatchEvent(event, EventTypes.DOWN);\n  };\n\n  private keyUpCallback = (event: KeyboardEvent): void => {\n    if (this.activationKeys.indexOf(event.key) === -1 || !this.isPressed) {\n      return;\n    }\n\n    this.dispatchEvent(event, EventTypes.UP);\n  };\n\n  private dispatchEvent(event: KeyboardEvent, eventType: EventTypes) {\n    if (!(event.target instanceof HTMLElement)) {\n      return;\n    }\n\n    const adaptedEvent = this.mapEvent(event, eventType);\n\n    switch (eventType) {\n      case EventTypes.UP:\n        this.isPressed = false;\n        this.onPointerUp(adaptedEvent);\n        break;\n      case EventTypes.DOWN:\n        this.isPressed = true;\n        this.onPointerDown(adaptedEvent);\n        break;\n      case EventTypes.CANCEL:\n        this.isPressed = false;\n        this.onPointerCancel(adaptedEvent);\n        break;\n    }\n  }\n\n  public registerListeners(): void {\n    this.view.addEventListener('keydown', this.keyDownCallback);\n    this.view.addEventListener('keyup', this.keyUpCallback);\n  }\n\n  public unregisterListeners(): void {\n    this.view.removeEventListener('keydown', this.keyDownCallback);\n    this.view.removeEventListener('keyup', this.keyUpCallback);\n  }\n\n  protected mapEvent(\n    event: KeyboardEvent,\n    eventType: EventTypes\n  ): AdaptedEvent {\n    const viewRect = (event.target as HTMLElement).getBoundingClientRect();\n\n    const viewportPosition = {\n      x: viewRect?.x + viewRect?.width / 2,\n      y: viewRect?.y + viewRect?.height / 2,\n    };\n\n    const relativePosition = {\n      x: viewRect?.width / 2,\n      y: viewRect?.height / 2,\n    };\n\n    return {\n      x: viewportPosition.x,\n      y: viewportPosition.y,\n      offsetX: relativePosition.x,\n      offsetY: relativePosition.y,\n      pointerId: 0,\n      eventType: eventType,\n      pointerType: PointerType.KEY,\n      time: event.timeStamp,\n    };\n  }\n}\n"]}