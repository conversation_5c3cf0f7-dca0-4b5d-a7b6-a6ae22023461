import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useApp } from '../store/AppContext';
import { RootStackParamList } from '../navigation/AppNavigator';
import { MusicService, LocationAccuracy } from '../types';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList>;

export default function HomeScreen() {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { state, actions } = useApp();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    // Load initial data
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      await actions.loadUserTags();
      await actions.loadPublicTags();
    } catch (error) {
      console.error('Error loading initial data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadInitialData();
    setRefreshing(false);
  };

  const handleStartLocationTracking = async () => {
    if (!state.locationPermissions.foreground) {
      Alert.alert(
        'Location Permission Required',
        'This app needs location access to function properly. Please grant location permissions.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Grant Permission', onPress: () => actions.startLocationTracking() },
        ]
      );
      return;
    }

    try {
      await actions.startLocationTracking();
    } catch (error) {
      Alert.alert('Error', 'Failed to start location tracking. Please try again.');
    }
  };

  const handleStopLocationTracking = async () => {
    try {
      await actions.stopLocationTracking();
    } catch (error) {
      Alert.alert('Error', 'Failed to stop location tracking.');
    }
  };

  const handleConnectSpotify = async () => {
    try {
      await actions.connectMusicService(MusicService.SPOTIFY);
      Alert.alert('Success', 'Spotify connected successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to connect to Spotify. Please try again.');
    }
  };

  const handleCreateTag = () => {
    if (!state.currentLocation) {
      Alert.alert(
        'Location Required',
        'Please enable location tracking to create a tag at your current location.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Enable Location', onPress: handleStartLocationTracking },
        ]
      );
      return;
    }

    navigation.navigate('CreateTag');
  };

  const isSpotifyConnected = state.connectedServices.some(
    service => service.service === MusicService.SPOTIFY && service.isActive
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Location Music</Text>
          <Text style={styles.subtitle}>
            Tag music to locations and let your surroundings set the mood
          </Text>
        </View>

        {/* Current Status */}
        <View style={styles.statusSection}>
          <Text style={styles.sectionTitle}>Current Status</Text>
          
          {/* Location Status */}
          <View style={styles.statusCard}>
            <View style={styles.statusHeader}>
              <Ionicons 
                name={state.isLocationTracking ? "location" : "location-outline"} 
                size={24} 
                color={state.isLocationTracking ? "#4CAF50" : "#757575"} 
              />
              <Text style={styles.statusTitle}>Location Tracking</Text>
            </View>
            <Text style={styles.statusText}>
              {state.isLocationTracking 
                ? `Active - ${state.currentLocation ? 'Location detected' : 'Searching...'}`
                : 'Inactive'
              }
            </Text>
            <TouchableOpacity
              style={[
                styles.statusButton,
                { backgroundColor: state.isLocationTracking ? '#f44336' : '#4CAF50' }
              ]}
              onPress={state.isLocationTracking ? handleStopLocationTracking : handleStartLocationTracking}
              disabled={state.isLoading}
            >
              <Text style={styles.statusButtonText}>
                {state.isLocationTracking ? 'Stop Tracking' : 'Start Tracking'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Music Service Status */}
          <View style={styles.statusCard}>
            <View style={styles.statusHeader}>
              <Ionicons 
                name={isSpotifyConnected ? "musical-notes" : "musical-notes-outline"} 
                size={24} 
                color={isSpotifyConnected ? "#1DB954" : "#757575"} 
              />
              <Text style={styles.statusTitle}>Music Services</Text>
            </View>
            <Text style={styles.statusText}>
              {isSpotifyConnected ? 'Spotify Connected' : 'No services connected'}
            </Text>
            {!isSpotifyConnected && (
              <TouchableOpacity
                style={[styles.statusButton, { backgroundColor: '#1DB954' }]}
                onPress={handleConnectSpotify}
                disabled={state.isLoading}
              >
                <Text style={styles.statusButtonText}>Connect Spotify</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Current Playing */}
          {state.currentTrack && (
            <View style={styles.statusCard}>
              <View style={styles.statusHeader}>
                <Ionicons 
                  name={state.isPlaying ? "play" : "pause"} 
                  size={24} 
                  color="#007AFF" 
                />
                <Text style={styles.statusTitle}>Now Playing</Text>
              </View>
              <Text style={styles.statusText}>
                {state.currentTrack.title} - {state.currentTrack.artist}
              </Text>
              <TouchableOpacity
                style={[styles.statusButton, { backgroundColor: '#007AFF' }]}
                onPress={() => navigation.navigate('Playback')}
              >
                <Text style={styles.statusButtonText}>View Player</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.actionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <View style={styles.actionGrid}>
            <TouchableOpacity
              style={styles.actionCard}
              onPress={handleCreateTag}
              disabled={state.isLoading}
            >
              <Ionicons name="add-circle" size={32} color="#007AFF" />
              <Text style={styles.actionTitle}>Create Tag</Text>
              <Text style={styles.actionSubtitle}>Tag music to current location</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => navigation.navigate('MainTabs', { screen: 'Map' })}
            >
              <Ionicons name="map" size={32} color="#007AFF" />
              <Text style={styles.actionTitle}>View Map</Text>
              <Text style={styles.actionSubtitle}>See all tagged locations</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => navigation.navigate('MainTabs', { screen: 'Tags' })}
            >
              <Ionicons name="bookmark" size={32} color="#007AFF" />
              <Text style={styles.actionTitle}>My Tags</Text>
              <Text style={styles.actionSubtitle}>Manage your location tags</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => navigation.navigate('MainTabs', { screen: 'Settings' })}
            >
              <Ionicons name="settings" size={32} color="#007AFF" />
              <Text style={styles.actionTitle}>Settings</Text>
              <Text style={styles.actionSubtitle}>Configure app preferences</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Statistics */}
        <View style={styles.statsSection}>
          <Text style={styles.sectionTitle}>Statistics</Text>
          
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{state.userTags.length}</Text>
              <Text style={styles.statLabel}>My Tags</Text>
            </View>
            
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{state.publicTags.length}</Text>
              <Text style={styles.statLabel}>Public Tags</Text>
            </View>
            
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{state.connectedServices.length}</Text>
              <Text style={styles.statLabel}>Connected Services</Text>
            </View>
          </View>
        </View>

        {/* Error Display */}
        {state.error && (
          <View style={styles.errorSection}>
            <View style={styles.errorCard}>
              <Ionicons name="warning" size={24} color="#f44336" />
              <Text style={styles.errorText}>{state.error.message}</Text>
              <TouchableOpacity
                style={styles.errorButton}
                onPress={actions.clearError}
              >
                <Text style={styles.errorButtonText}>Dismiss</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    backgroundColor: '#007AFF',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 22,
  },
  statusSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
    color: '#333',
  },
  statusCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
    color: '#333',
  },
  statusText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  statusButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  statusButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  actionsSection: {
    padding: 20,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    width: '48%',
    marginBottom: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
    marginBottom: 4,
    color: '#333',
    textAlign: 'center',
  },
  actionSubtitle: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  statsSection: {
    padding: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    flex: 1,
    marginHorizontal: 4,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  errorSection: {
    padding: 20,
  },
  errorCard: {
    backgroundColor: '#ffebee',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderLeftWidth: 4,
    borderLeftColor: '#f44336',
  },
  errorText: {
    flex: 1,
    marginLeft: 12,
    color: '#d32f2f',
    fontSize: 14,
  },
  errorButton: {
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  errorButtonText: {
    color: '#d32f2f',
    fontWeight: '600',
    fontSize: 12,
  },
});
