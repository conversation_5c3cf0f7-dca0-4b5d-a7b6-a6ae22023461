{"version": 3, "sources": ["tapGesture.ts"], "names": ["TapGesture", "BaseGesture", "constructor", "handler<PERSON>ame", "shouldCancelWhenOutside", "minPointers", "config", "numberOfTaps", "count", "maxDistance", "maxDist", "maxDuration", "duration", "maxDurationMs", "max<PERSON><PERSON><PERSON>", "delay", "max<PERSON>elay<PERSON>", "maxDeltaX", "delta", "maxDeltaY"], "mappings": ";;;;;;;AAAA;;;;AAIO,MAAMA,UAAN,SAAyBC,oBAAzB,CAAoE;AAGzEC,EAAAA,WAAW,GAAG;AACZ;;AADY,oCAFwC,EAExC;;AAGZ,SAAKC,WAAL,GAAmB,mBAAnB;AACA,SAAKC,uBAAL,CAA6B,IAA7B;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEC,EAAAA,WAAW,CAACA,WAAD,EAAsB;AAC/B,SAAKC,MAAL,CAAYD,WAAZ,GAA0BA,WAA1B;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEE,EAAAA,YAAY,CAACC,KAAD,EAAgB;AAC1B,SAAKF,MAAL,CAAYC,YAAZ,GAA2BC,KAA3B;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEC,EAAAA,WAAW,CAACC,OAAD,EAAkB;AAC3B,SAAKJ,MAAL,CAAYI,OAAZ,GAAsBA,OAAtB;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEC,EAAAA,WAAW,CAACC,QAAD,EAAmB;AAC5B,SAAKN,MAAL,CAAYO,aAAZ,GAA4BD,QAA5B;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEE,EAAAA,QAAQ,CAACC,KAAD,EAAgB;AACtB,SAAKT,MAAL,CAAYU,UAAZ,GAAyBD,KAAzB;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEE,EAAAA,SAAS,CAACC,KAAD,EAAgB;AACvB,SAAKZ,MAAL,CAAYW,SAAZ,GAAwBC,KAAxB;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEC,EAAAA,SAAS,CAACD,KAAD,EAAgB;AACvB,SAAKZ,MAAL,CAAYa,SAAZ,GAAwBD,KAAxB;AACA,WAAO,IAAP;AACD;;AA9EwE", "sourcesContent": ["import { BaseGestureConfig, BaseGesture } from './gesture';\nimport { TapGestureConfig } from '../TapGestureHandler';\nimport type { TapGestureHandlerEventPayload } from '../GestureHandlerEventPayload';\n\nexport class TapGesture extends BaseGesture<TapGestureHandlerEventPayload> {\n  public config: BaseGestureConfig & TapGestureConfig = {};\n\n  constructor() {\n    super();\n\n    this.handlerName = 'TapGestureHandler';\n    this.shouldCancelWhenOutside(true);\n  }\n\n  /**\n   * Minimum number of pointers (fingers) required to be placed before the gesture activates.\n   * Should be a positive integer. The default value is 1.\n   * @param minPointers\n   */\n  minPointers(minPointers: number) {\n    this.config.minPointers = minPointers;\n    return this;\n  }\n\n  /**\n   * Number of tap gestures required to activate the gesture.\n   * The default value is 1.\n   * @param count\n   */\n  numberOfTaps(count: number) {\n    this.config.numberOfTaps = count;\n    return this;\n  }\n\n  /**\n   * Maximum distance, expressed in points, that defines how far the finger is allowed to travel during a tap gesture.\n   * @param maxDist\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/tap-gesture#maxdistancevalue-number\n   */\n  maxDistance(maxDist: number) {\n    this.config.maxDist = maxDist;\n    return this;\n  }\n\n  /**\n   * Maximum time, expressed in milliseconds, that defines how fast a finger must be released after a touch.\n   * The default value is 500.\n   * @param duration\n   */\n  maxDuration(duration: number) {\n    this.config.maxDurationMs = duration;\n    return this;\n  }\n\n  /**\n   * Maximum time, expressed in milliseconds, that can pass before the next tap — if many taps are required.\n   * The default value is 500.\n   * @param delay\n   */\n  maxDelay(delay: number) {\n    this.config.maxDelayMs = delay;\n    return this;\n  }\n\n  /**\n   * Maximum distance, expressed in points, that defines how far the finger is allowed to travel along the X axis during a tap gesture.\n   * @param delta\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/tap-gesture#maxdeltaxvalue-number\n   */\n  maxDeltaX(delta: number) {\n    this.config.maxDeltaX = delta;\n    return this;\n  }\n\n  /**\n   * Maximum distance, expressed in points, that defines how far the finger is allowed to travel along the Y axis during a tap gesture.\n   * @param delta\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/tap-gesture#maxdeltayvalue-number\n   */\n  maxDeltaY(delta: number) {\n    this.config.maxDeltaY = delta;\n    return this;\n  }\n}\n\nexport type TapGestureType = InstanceType<typeof TapGesture>;\n"]}