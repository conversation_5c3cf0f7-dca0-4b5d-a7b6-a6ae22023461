import * as SQLite from 'expo-sqlite';
import { LocationTag, Track, Playlist, User, ConnectedService, TagType, TagCategory, MusicService } from '../types';

export class DatabaseService {
  private static instance: DatabaseService;
  private db: SQLite.SQLiteDatabase | null = null;

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  public async initialize(): Promise<void> {
    try {
      this.db = await SQLite.openDatabaseAsync('locationmusic.db');
      await this.createTables();
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Database initialization error:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const createTablesSQL = `
      -- Users table
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        display_name TEXT,
        avatar TEXT,
        preferences TEXT, -- JSON string
        created_at INTEGER NOT NULL
      );

      -- Connected services table
      CREATE TABLE IF NOT EXISTS connected_services (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        service TEXT NOT NULL,
        access_token TEXT NOT NULL,
        refresh_token TEXT,
        expires_at INTEGER,
        service_user_id TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        connected_at INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id),
        UNIQUE(user_id, service)
      );

      -- Location tags table
      CREATE TABLE IF NOT EXISTS location_tags (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        accuracy REAL,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        radius REAL,
        route_points TEXT, -- JSON string for route segments
        metadata TEXT, -- JSON string
        is_public INTEGER DEFAULT 0,
        user_id TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      );

      -- Tracks table
      CREATE TABLE IF NOT EXISTS tracks (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        artist TEXT NOT NULL,
        album TEXT,
        duration INTEGER,
        artwork TEXT,
        service TEXT NOT NULL,
        service_track_id TEXT NOT NULL,
        preview_url TEXT,
        created_at INTEGER NOT NULL
      );

      -- Playlists table
      CREATE TABLE IF NOT EXISTS playlists (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        service TEXT NOT NULL,
        service_playlist_id TEXT NOT NULL,
        artwork TEXT,
        created_at INTEGER NOT NULL
      );

      -- Tag tracks relationship table
      CREATE TABLE IF NOT EXISTS tag_tracks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tag_id TEXT NOT NULL,
        track_id TEXT NOT NULL,
        added_at INTEGER NOT NULL,
        FOREIGN KEY (tag_id) REFERENCES location_tags (id) ON DELETE CASCADE,
        FOREIGN KEY (track_id) REFERENCES tracks (id) ON DELETE CASCADE,
        UNIQUE(tag_id, track_id)
      );

      -- Tag playlists relationship table
      CREATE TABLE IF NOT EXISTS tag_playlists (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tag_id TEXT NOT NULL,
        playlist_id TEXT NOT NULL,
        added_at INTEGER NOT NULL,
        FOREIGN KEY (tag_id) REFERENCES location_tags (id) ON DELETE CASCADE,
        FOREIGN KEY (playlist_id) REFERENCES playlists (id) ON DELETE CASCADE,
        UNIQUE(tag_id, playlist_id)
      );

      -- Playlist tracks relationship table
      CREATE TABLE IF NOT EXISTS playlist_tracks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        playlist_id TEXT NOT NULL,
        track_id TEXT NOT NULL,
        position INTEGER NOT NULL,
        added_at INTEGER NOT NULL,
        FOREIGN KEY (playlist_id) REFERENCES playlists (id) ON DELETE CASCADE,
        FOREIGN KEY (track_id) REFERENCES tracks (id) ON DELETE CASCADE,
        UNIQUE(playlist_id, track_id)
      );

      -- Create indexes for better performance
      CREATE INDEX IF NOT EXISTS idx_location_tags_user_id ON location_tags (user_id);
      CREATE INDEX IF NOT EXISTS idx_location_tags_location ON location_tags (latitude, longitude);
      CREATE INDEX IF NOT EXISTS idx_location_tags_category ON location_tags (category);
      CREATE INDEX IF NOT EXISTS idx_location_tags_public ON location_tags (is_public);
      CREATE INDEX IF NOT EXISTS idx_connected_services_user_id ON connected_services (user_id);
      CREATE INDEX IF NOT EXISTS idx_tag_tracks_tag_id ON tag_tracks (tag_id);
      CREATE INDEX IF NOT EXISTS idx_tag_playlists_tag_id ON tag_playlists (tag_id);
    `;

    await this.db.execAsync(createTablesSQL);
  }

  // User Management
  public async saveUser(user: User): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync(
      `INSERT OR REPLACE INTO users (id, email, display_name, avatar, preferences, created_at)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [
        user.id,
        user.email,
        user.displayName || null,
        user.avatar || null,
        JSON.stringify(user.preferences),
        user.createdAt,
      ]
    );
  }

  public async getUser(userId: string): Promise<User | null> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getFirstAsync(
      'SELECT * FROM users WHERE id = ?',
      [userId]
    ) as any;

    if (!result) return null;

    return {
      id: result.id,
      email: result.email,
      displayName: result.display_name,
      avatar: result.avatar,
      preferences: JSON.parse(result.preferences),
      connectedServices: await this.getConnectedServices(userId),
      createdAt: result.created_at,
    };
  }

  // Connected Services Management
  public async saveConnectedService(service: ConnectedService): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync(
      `INSERT OR REPLACE INTO connected_services 
       (user_id, service, access_token, refresh_token, expires_at, service_user_id, is_active, connected_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        service.userId,
        service.service,
        service.accessToken,
        service.refreshToken || null,
        service.expiresAt || null,
        service.userId,
        service.isActive ? 1 : 0,
        service.connectedAt,
      ]
    );
  }

  public async getConnectedServices(userId: string): Promise<ConnectedService[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(
      'SELECT * FROM connected_services WHERE user_id = ? AND is_active = 1',
      [userId]
    ) as any[];

    return results.map(row => ({
      service: row.service as MusicService,
      accessToken: row.access_token,
      refreshToken: row.refresh_token,
      expiresAt: row.expires_at,
      userId: row.service_user_id,
      isActive: row.is_active === 1,
      connectedAt: row.connected_at,
    }));
  }

  // Location Tags Management
  public async saveLocationTag(tag: LocationTag): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync(
      `INSERT OR REPLACE INTO location_tags 
       (id, name, description, latitude, longitude, accuracy, type, category, radius, route_points, metadata, is_public, user_id, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        tag.id,
        tag.name,
        tag.description || null,
        tag.location.latitude,
        tag.location.longitude,
        tag.location.accuracy || null,
        tag.type,
        tag.category,
        tag.radius || null,
        tag.routePoints ? JSON.stringify(tag.routePoints) : null,
        JSON.stringify(tag.metadata),
        tag.isPublic ? 1 : 0,
        tag.userId,
        tag.createdAt,
        tag.updatedAt,
      ]
    );

    // Save associated tracks and playlists
    await this.saveTagTracks(tag.id, tag.tracks);
    await this.saveTagPlaylists(tag.id, tag.playlists);
  }

  public async getLocationTag(tagId: string): Promise<LocationTag | null> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getFirstAsync(
      'SELECT * FROM location_tags WHERE id = ?',
      [tagId]
    ) as any;

    if (!result) return null;

    const tracks = await this.getTagTracks(tagId);
    const playlists = await this.getTagPlaylists(tagId);

    return {
      id: result.id,
      name: result.name,
      description: result.description,
      location: {
        latitude: result.latitude,
        longitude: result.longitude,
        accuracy: result.accuracy,
        timestamp: result.created_at,
      },
      type: result.type as TagType,
      category: result.category as TagCategory,
      radius: result.radius,
      routePoints: result.route_points ? JSON.parse(result.route_points) : undefined,
      tracks,
      playlists,
      metadata: JSON.parse(result.metadata),
      isPublic: result.is_public === 1,
      userId: result.user_id,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
    };
  }

  public async getUserLocationTags(userId: string): Promise<LocationTag[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(
      'SELECT * FROM location_tags WHERE user_id = ? ORDER BY updated_at DESC',
      [userId]
    ) as any[];

    const tags: LocationTag[] = [];
    for (const result of results) {
      const tracks = await this.getTagTracks(result.id);
      const playlists = await this.getTagPlaylists(result.id);

      tags.push({
        id: result.id,
        name: result.name,
        description: result.description,
        location: {
          latitude: result.latitude,
          longitude: result.longitude,
          accuracy: result.accuracy,
          timestamp: result.created_at,
        },
        type: result.type as TagType,
        category: result.category as TagCategory,
        radius: result.radius,
        routePoints: result.route_points ? JSON.parse(result.route_points) : undefined,
        tracks,
        playlists,
        metadata: JSON.parse(result.metadata),
        isPublic: result.is_public === 1,
        userId: result.user_id,
        createdAt: result.created_at,
        updatedAt: result.updated_at,
      });
    }

    return tags;
  }

  public async getPublicLocationTags(limit: number = 100): Promise<LocationTag[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(
      'SELECT * FROM location_tags WHERE is_public = 1 ORDER BY updated_at DESC LIMIT ?',
      [limit]
    ) as any[];

    const tags: LocationTag[] = [];
    for (const result of results) {
      const tracks = await this.getTagTracks(result.id);
      const playlists = await this.getTagPlaylists(result.id);

      tags.push({
        id: result.id,
        name: result.name,
        description: result.description,
        location: {
          latitude: result.latitude,
          longitude: result.longitude,
          accuracy: result.accuracy,
          timestamp: result.created_at,
        },
        type: result.type as TagType,
        category: result.category as TagCategory,
        radius: result.radius,
        routePoints: result.route_points ? JSON.parse(result.route_points) : undefined,
        tracks,
        playlists,
        metadata: JSON.parse(result.metadata),
        isPublic: result.is_public === 1,
        userId: result.user_id,
        createdAt: result.created_at,
        updatedAt: result.updated_at,
      });
    }

    return tags;
  }

  public async deleteLocationTag(tagId: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync('DELETE FROM location_tags WHERE id = ?', [tagId]);
  }

  // Track Management
  public async saveTrack(track: Track): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync(
      `INSERT OR REPLACE INTO tracks (id, title, artist, album, duration, artwork, service, service_track_id, preview_url, created_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        track.id,
        track.title,
        track.artist,
        track.album || null,
        track.duration || null,
        track.artwork || null,
        track.service,
        track.serviceTrackId,
        track.previewUrl || null,
        Date.now(),
      ]
    );
  }

  // Private helper methods
  private async saveTagTracks(tagId: string, tracks: Track[]): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Clear existing associations
    await this.db.runAsync('DELETE FROM tag_tracks WHERE tag_id = ?', [tagId]);

    // Save tracks and create associations
    for (const track of tracks) {
      await this.saveTrack(track);
      await this.db.runAsync(
        'INSERT INTO tag_tracks (tag_id, track_id, added_at) VALUES (?, ?, ?)',
        [tagId, track.id, Date.now()]
      );
    }
  }

  private async saveTagPlaylists(tagId: string, playlists: Playlist[]): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Clear existing associations
    await this.db.runAsync('DELETE FROM tag_playlists WHERE tag_id = ?', [tagId]);

    // Save playlists and create associations
    for (const playlist of playlists) {
      await this.savePlaylist(playlist);
      await this.db.runAsync(
        'INSERT INTO tag_playlists (tag_id, playlist_id, added_at) VALUES (?, ?, ?)',
        [tagId, playlist.id, Date.now()]
      );
    }
  }

  private async savePlaylist(playlist: Playlist): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync(
      `INSERT OR REPLACE INTO playlists (id, name, description, service, service_playlist_id, artwork, created_at)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        playlist.id,
        playlist.name,
        playlist.description || null,
        playlist.service,
        playlist.servicePlaylistId,
        playlist.artwork || null,
        Date.now(),
      ]
    );

    // Save playlist tracks
    await this.db.runAsync('DELETE FROM playlist_tracks WHERE playlist_id = ?', [playlist.id]);
    
    for (let i = 0; i < playlist.tracks.length; i++) {
      const track = playlist.tracks[i];
      await this.saveTrack(track);
      await this.db.runAsync(
        'INSERT INTO playlist_tracks (playlist_id, track_id, position, added_at) VALUES (?, ?, ?, ?)',
        [playlist.id, track.id, i, Date.now()]
      );
    }
  }

  private async getTagTracks(tagId: string): Promise<Track[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(
      `SELECT t.* FROM tracks t
       JOIN tag_tracks tt ON t.id = tt.track_id
       WHERE tt.tag_id = ?
       ORDER BY tt.added_at`,
      [tagId]
    ) as any[];

    return results.map(row => ({
      id: row.id,
      title: row.title,
      artist: row.artist,
      album: row.album,
      duration: row.duration,
      artwork: row.artwork,
      service: row.service as MusicService,
      serviceTrackId: row.service_track_id,
      previewUrl: row.preview_url,
    }));
  }

  private async getTagPlaylists(tagId: string): Promise<Playlist[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(
      `SELECT p.* FROM playlists p
       JOIN tag_playlists tp ON p.id = tp.playlist_id
       WHERE tp.tag_id = ?
       ORDER BY tp.added_at`,
      [tagId]
    ) as any[];

    const playlists: Playlist[] = [];
    for (const row of results) {
      const tracks = await this.getPlaylistTracks(row.id);
      playlists.push({
        id: row.id,
        name: row.name,
        description: row.description,
        service: row.service as MusicService,
        servicePlaylistId: row.service_playlist_id,
        artwork: row.artwork,
        tracks,
      });
    }

    return playlists;
  }

  private async getPlaylistTracks(playlistId: string): Promise<Track[]> {
    if (!this.db) throw new Error('Database not initialized');

    const results = await this.db.getAllAsync(
      `SELECT t.* FROM tracks t
       JOIN playlist_tracks pt ON t.id = pt.track_id
       WHERE pt.playlist_id = ?
       ORDER BY pt.position`,
      [playlistId]
    ) as any[];

    return results.map(row => ({
      id: row.id,
      title: row.title,
      artist: row.artist,
      album: row.album,
      duration: row.duration,
      artwork: row.artwork,
      service: row.service as MusicService,
      serviceTrackId: row.service_track_id,
      previewUrl: row.preview_url,
    }));
  }
}

export default DatabaseService.getInstance();
