{"version": 3, "sources": ["GestureHandlerButton.web.tsx"], "names": ["React", "forwardRef", "props", "ref"], "mappings": ";;;;;;;AAAA;;AACA;;;;;;;;4BAEeA,KAAK,CAACC,UAAN,CAAuB,CAACC,KAAD,EAAQC,GAAR,kBACpC,oBAAC,iBAAD;AAAM,EAAA,GAAG,EAAEA,GAAX;AAAgB,EAAA,iBAAiB,EAAC;AAAlC,GAA+CD,KAA/C,EADa,C", "sourcesContent": ["import * as React from 'react';\nimport { View } from 'react-native';\n\nexport default React.forwardRef<View>((props, ref) => (\n  <View ref={ref} accessibilityRole=\"button\" {...props} />\n));\n"]}