{"version": 3, "sources": ["mountRegistry.ts"], "names": ["MountRegistry", "addMountListener", "listener", "mountListeners", "add", "delete", "addUnmountListener", "unmountListeners", "gestureHandlerWillMount", "handler", "for<PERSON>ach", "gestureHandlerWillUnmount", "gestureWillMount", "gesture", "gestureWillUnmount", "Set"], "mappings": ";;;;;;;;;AAUA;AACO,MAAMA,aAAN,CAAoB;AAIF,SAAhBC,gBAAgB,CAACC,QAAD,EAA6C;AAClE,SAAKC,cAAL,CAAoBC,GAApB,CAAwBF,QAAxB;AAEA,WAAO,MAAM;AACX,WAAKC,cAAL,CAAoBE,MAApB,CAA2BH,QAA3B;AACD,KAFD;AAGD;;AAEwB,SAAlBI,kBAAkB,CAACJ,QAAD,EAA6C;AACpE,SAAKK,gBAAL,CAAsBH,GAAtB,CAA0BF,QAA1B;AAEA,WAAO,MAAM;AACX,WAAKK,gBAAL,CAAsBF,MAAtB,CAA6BH,QAA7B;AACD,KAFD;AAGD;;AAE6B,SAAvBM,uBAAuB,CAACC,OAAD,EAA2B;AACvD,SAAKN,cAAL,CAAoBO,OAApB,CAA6BR,QAAD,IAC1BA,QAAQ,CAACO,OAAD,CADV;AAGD;;AAE+B,SAAzBE,yBAAyB,CAACF,OAAD,EAA2B;AACzD,SAAKF,gBAAL,CAAsBG,OAAtB,CAA+BR,QAAD,IAC5BA,QAAQ,CAACO,OAAD,CADV;AAGD;;AAEsB,SAAhBG,gBAAgB,CAACC,OAAD,EAAuB;AAC5C,SAAKV,cAAL,CAAoBO,OAApB,CAA6BR,QAAD,IAAcA,QAAQ,CAACW,OAAD,CAAlD;AACD;;AAEwB,SAAlBC,kBAAkB,CAACD,OAAD,EAAuB;AAC9C,SAAKN,gBAAL,CAAsBG,OAAtB,CAA+BR,QAAD,IAAcA,QAAQ,CAACW,OAAD,CAApD;AACD;;AAtCwB;;;;gBAAdb,a,oBACqB,IAAIe,GAAJ,E;;gBADrBf,a,sBAEuB,IAAIe,GAAJ,E", "sourcesContent": ["import { GestureType } from './handlers/gestures/gesture';\n\ninterface ReactComponentWithHandlerTag extends React.Component {\n  handlerTag: number;\n}\n\nexport type GestureMountListener = (\n  gesture: GestureType | ReactComponentWithHandlerTag\n) => void;\n\n// eslint-disable-next-line @typescript-eslint/no-extraneous-class\nexport class MountRegistry {\n  private static mountListeners = new Set<GestureMountListener>();\n  private static unmountListeners = new Set<GestureMountListener>();\n\n  static addMountListener(listener: GestureMountListener): () => void {\n    this.mountListeners.add(listener);\n\n    return () => {\n      this.mountListeners.delete(listener);\n    };\n  }\n\n  static addUnmountListener(listener: GestureMountListener): () => void {\n    this.unmountListeners.add(listener);\n\n    return () => {\n      this.unmountListeners.delete(listener);\n    };\n  }\n\n  static gestureHandlerWillMount(handler: React.Component) {\n    this.mountListeners.forEach((listener) =>\n      listener(handler as ReactComponentWithHandlerTag)\n    );\n  }\n\n  static gestureHandlerWillUnmount(handler: React.Component) {\n    this.unmountListeners.forEach((listener) =>\n      listener(handler as ReactComponentWithHandlerTag)\n    );\n  }\n\n  static gestureWillMount(gesture: GestureType) {\n    this.mountListeners.forEach((listener) => listener(gesture));\n  }\n\n  static gestureWillUnmount(gesture: GestureType) {\n    this.unmountListeners.forEach((listener) => listener(gesture));\n  }\n}\n"]}