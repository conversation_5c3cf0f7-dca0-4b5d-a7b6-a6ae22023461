{"version": 3, "file": "LogBoxLog.d.ts", "sourceRoot": "", "sources": ["../../../src/error-overlay/Data/LogBoxLog.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AAErF,KAAK,mBAAmB,GAAG,MAAM,GAAG,SAAS,GAAG,UAAU,GAAG,QAAQ,CAAC;AAEtE,MAAM,MAAM,QAAQ,GAAG,MAAM,GAAG,OAAO,GAAG,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAExE,MAAM,MAAM,aAAa,GAAG;IAC1B,KAAK,EAAE,QAAQ,CAAC;IAChB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,OAAO,CAAC;IACjB,KAAK,EAAE,KAAK,CAAC;IACb,QAAQ,EAAE,MAAM,CAAC;IACjB,cAAc,EAAE,cAAc,CAAC;IAC/B,SAAS,CAAC,EAAE,SAAS,CAAC;IACtB,gBAAgB,EAAE,OAAO,CAAC;CAC3B,CAAC;AAEF,MAAM,MAAM,SAAS,GAAG,OAAO,GAAG,WAAW,CAAC;AAc9C,KAAK,mBAAmB,GACpB;IAAE,KAAK,EAAE,IAAI,CAAC;IAAC,KAAK,EAAE,IAAI,CAAC;IAAC,MAAM,EAAE,MAAM,CAAA;CAAE,GAC5C;IAAE,KAAK,EAAE,IAAI,CAAC;IAAC,KAAK,EAAE,IAAI,CAAC;IAAC,MAAM,EAAE,SAAS,CAAA;CAAE,GAC/C;IAAE,KAAK,EAAE,IAAI,CAAC;IAAC,KAAK,EAAE,KAAK,CAAC;IAAC,MAAM,EAAE,UAAU,CAAA;CAAE,GACjD;IAAE,KAAK,EAAE,KAAK,CAAC;IAAC,KAAK,EAAE,IAAI,CAAC;IAAC,MAAM,EAAE,QAAQ,CAAA;CAAE,CAAC;AAEpD,qBAAa,SAAS;IACpB,OAAO,EAAE,OAAO,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,EAAE,QAAQ,CAAC;IACnB,cAAc,EAAE,cAAc,CAAC;IAC/B,KAAK,EAAE,KAAK,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,QAAQ,CAAC;IAChB,SAAS,CAAC,EAAE,SAAS,CAAC;IACtB,gBAAgB,EAAE,OAAO,CAAC;IAC1B,YAAY,EAAE,MAAM,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAWlD;IAEF,OAAO,CAAC,SAAS,CAAyD;gBAGxE,IAAI,EAAE,aAAa,GAAG;QACpB,YAAY,CAAC,EAAE,MAAM,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;KACvD;IAcH,cAAc,IAAI,IAAI;IAItB,iBAAiB,CAAC,IAAI,EAAE,SAAS,GAAG,KAAK,GAAG,IAAI;IAOhD,OAAO,CAAC,cAAc;IAWtB,OAAO,CAAC,YAAY;IASpB,gBAAgB,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,mBAAmB,KAAK,IAAI,GAAG,IAAI;IAIzF,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,mBAAmB,KAAK,IAAI,GAAG,IAAI;IAIpF,OAAO,CAAC,YAAY;IAwBpB,OAAO,CAAC,mBAAmB,CAAsB;IAEjD,OAAO,CAAC,QAAQ;IAUhB,OAAO,CAAC,iBAAiB;IAkBzB,OAAO,CAAC,YAAY;CAsCrB"}