{"version": 3, "sources": ["FlingGestureHandler.ts"], "names": ["State", "DiagonalDirections", "Directions", "Gesture<PERSON>andler", "Vector", "coneToDeviation", "DEFAULT_MAX_DURATION_MS", "DEFAULT_MIN_VELOCITY", "DEFAULT_ALIGNMENT_CONE", "DEFAULT_DIRECTION", "RIGHT", "DEFAULT_NUMBER_OF_TOUCHES_REQUIRED", "AXIAL_DEVIATION_COSINE", "DIAGONAL_DEVIATION_COSINE", "FlingGestureHandler", "NaN", "updateGestureConfig", "enabled", "props", "config", "direction", "numberOfPointers", "numberOfPointersRequired", "startFling", "begin", "maxNumberOfPointersSimultaneously", "delayTimeout", "setTimeout", "fail", "maxDurationMs", "tryEndFling", "velocityVector", "fromVelocity", "tracker", "keyPointer", "getAlignment", "minimalAlignmentCosine", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fromDirection", "axialDirectionsList", "Object", "values", "diagonalDirectionsList", "axialAlignmentList", "map", "diagonalAlignmentList", "isAligned", "some", "Boolean", "isFast", "magnitude", "minVelocity", "clearTimeout", "activate", "endFling", "onPointerDown", "event", "isButtonInConfig", "button", "addToTracker", "pointerId", "newPointerAction", "tryToSendTouchEvent", "onPointerAdd", "state", "UNDETERMINED", "BEGAN", "trackedPointersCount", "pointerMoveAction", "track", "onPointerMove", "onPointerOutOfBounds", "onPointerUp", "onUp", "onPointerRemove", "removeFromTracker", "force", "end", "resetConfig"], "mappings": ";;AAAA,SAASA,KAAT,QAAsB,aAAtB;AACA,SAASC,kBAAT,EAA6BC,UAA7B,QAA+C,kBAA/C;AAGA,OAAOC,cAAP,MAA2B,kBAA3B;AACA,OAAOC,MAAP,MAAmB,iBAAnB;AACA,SAASC,eAAT,QAAgC,UAAhC;AAEA,MAAMC,uBAAuB,GAAG,GAAhC;AACA,MAAMC,oBAAoB,GAAG,GAA7B;AACA,MAAMC,sBAAsB,GAAG,EAA/B;AACA,MAAMC,iBAAiB,GAAGP,UAAU,CAACQ,KAArC;AACA,MAAMC,kCAAkC,GAAG,CAA3C;AAEA,MAAMC,sBAAsB,GAAGP,eAAe,CAACG,sBAAD,CAA9C;AACA,MAAMK,yBAAyB,GAAGR,eAAe,CAAC,KAAKG,sBAAN,CAAjD;AAEA,eAAe,MAAMM,mBAAN,SAAkCX,cAAlC,CAAiD;AAAA;AAAA;;AAAA,sDAC3BQ,kCAD2B;;AAAA,uCAE9BF,iBAF8B;;AAAA,2CAItCH,uBAJsC;;AAAA,yCAKxCC,oBALwC;;AAAA;;AAAA,+DAQlB,CARkB;;AAAA,wCASzCQ,GATyC;AAAA;;AAWvDC,EAAAA,mBAAmB,CAAC;AAAEC,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAGC;AAArB,GAAD,EAA6C;AACrE,UAAMF,mBAAN,CAA0B;AAAEC,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAGC;AAAvB,KAA1B;;AAEA,QAAI,KAAKC,MAAL,CAAYC,SAAhB,EAA2B;AACzB,WAAKA,SAAL,GAAiB,KAAKD,MAAL,CAAYC,SAA7B;AACD;;AAED,QAAI,KAAKD,MAAL,CAAYE,gBAAhB,EAAkC;AAChC,WAAKC,wBAAL,GAAgC,KAAKH,MAAL,CAAYE,gBAA5C;AACD;AACF;;AAEOE,EAAAA,UAAU,GAAS;AACzB,SAAKC,KAAL;AAEA,SAAKC,iCAAL,GAAyC,CAAzC;AAEA,SAAKC,YAAL,GAAoBC,UAAU,CAAC,MAAM,KAAKC,IAAL,EAAP,EAAoB,KAAKC,aAAzB,CAA9B;AACD;;AAEOC,EAAAA,WAAW,GAAY;AAC7B,UAAMC,cAAc,GAAG3B,MAAM,CAAC4B,YAAP,CAAoB,KAAKC,OAAzB,EAAkC,KAAKC,UAAvC,CAAvB;;AAEA,UAAMC,YAAY,GAAG,CACnBf,SADmB,EAEnBgB,sBAFmB,KAGhB;AACH,aACE,CAAChB,SAAS,GAAG,KAAKA,SAAlB,MAAiCA,SAAjC,IACAW,cAAc,CAACM,SAAf,CACEjC,MAAM,CAACkC,aAAP,CAAqBlB,SAArB,CADF,EAEEgB,sBAFF,CAFF;AAOD,KAXD;;AAaA,UAAMG,mBAAmB,GAAGC,MAAM,CAACC,MAAP,CAAcvC,UAAd,CAA5B;AACA,UAAMwC,sBAAsB,GAAGF,MAAM,CAACC,MAAP,CAAcxC,kBAAd,CAA/B,CAjB6B,CAmB7B;;AACA,UAAM0C,kBAAkB,GAAGJ,mBAAmB,CAACK,GAApB,CAAyBxB,SAAD,IACjDe,YAAY,CAACf,SAAD,EAAYR,sBAAZ,CADa,CAA3B;AAIA,UAAMiC,qBAAqB,GAAGH,sBAAsB,CAACE,GAAvB,CAA4BxB,SAAD,IACvDe,YAAY,CAACf,SAAD,EAAYP,yBAAZ,CADgB,CAA9B;AAIA,UAAMiC,SAAS,GACbH,kBAAkB,CAACI,IAAnB,CAAwBC,OAAxB,KAAoCH,qBAAqB,CAACE,IAAtB,CAA2BC,OAA3B,CADtC;AAGA,UAAMC,MAAM,GAAGlB,cAAc,CAACmB,SAAf,GAA2B,KAAKC,WAA/C;;AAEA,QACE,KAAK1B,iCAAL,KACE,KAAKH,wBADP,IAEAwB,SAFA,IAGAG,MAJF,EAKE;AACAG,MAAAA,YAAY,CAAC,KAAK1B,YAAN,CAAZ;AACA,WAAK2B,QAAL;AAEA,aAAO,IAAP;AACD;;AAED,WAAO,KAAP;AACD;;AAEOC,EAAAA,QAAQ,GAAG;AACjB,QAAI,CAAC,KAAKxB,WAAL,EAAL,EAAyB;AACvB,WAAKF,IAAL;AACD;AACF;;AAES2B,EAAAA,aAAa,CAACC,KAAD,EAA4B;AACjD,QAAI,CAAC,KAAKC,gBAAL,CAAsBD,KAAK,CAACE,MAA5B,CAAL,EAA0C;AACxC;AACD;;AAED,SAAKzB,OAAL,CAAa0B,YAAb,CAA0BH,KAA1B;AACA,SAAKtB,UAAL,GAAkBsB,KAAK,CAACI,SAAxB;AAEA,UAAML,aAAN,CAAoBC,KAApB;AACA,SAAKK,gBAAL;AAEA,SAAKC,mBAAL,CAAyBN,KAAzB;AACD;;AAESO,EAAAA,YAAY,CAACP,KAAD,EAA4B;AAChD,SAAKvB,OAAL,CAAa0B,YAAb,CAA0BH,KAA1B;AACA,UAAMO,YAAN,CAAmBP,KAAnB;AACA,SAAKK,gBAAL;AACD;;AAEOA,EAAAA,gBAAgB,GAAS;AAC/B,QAAI,KAAKG,KAAL,KAAehE,KAAK,CAACiE,YAAzB,EAAuC;AACrC,WAAK1C,UAAL;AACD;;AAED,QAAI,KAAKyC,KAAL,KAAehE,KAAK,CAACkE,KAAzB,EAAgC;AAC9B;AACD;;AAED,SAAKpC,WAAL;;AAEA,QACE,KAAKG,OAAL,CAAakC,oBAAb,GAAoC,KAAK1C,iCAD3C,EAEE;AACA,WAAKA,iCAAL,GACE,KAAKQ,OAAL,CAAakC,oBADf;AAED;AACF;;AAEOC,EAAAA,iBAAiB,CAACZ,KAAD,EAA4B;AACnD,SAAKvB,OAAL,CAAaoC,KAAb,CAAmBb,KAAnB;;AAEA,QAAI,KAAKQ,KAAL,KAAehE,KAAK,CAACkE,KAAzB,EAAgC;AAC9B;AACD;;AAED,SAAKpC,WAAL;AACD;;AAESwC,EAAAA,aAAa,CAACd,KAAD,EAA4B;AACjD,SAAKY,iBAAL,CAAuBZ,KAAvB;AACA,UAAMc,aAAN,CAAoBd,KAApB;AACD;;AAESe,EAAAA,oBAAoB,CAACf,KAAD,EAA4B;AACxD,SAAKY,iBAAL,CAAuBZ,KAAvB;AACA,UAAMe,oBAAN,CAA2Bf,KAA3B;AACD;;AAESgB,EAAAA,WAAW,CAAChB,KAAD,EAA4B;AAC/C,UAAMgB,WAAN,CAAkBhB,KAAlB;AACA,SAAKiB,IAAL,CAAUjB,KAAV;AAEA,SAAKtB,UAAL,GAAkBnB,GAAlB;AACD;;AAES2D,EAAAA,eAAe,CAAClB,KAAD,EAA4B;AACnD,UAAMkB,eAAN,CAAsBlB,KAAtB;AACA,SAAKiB,IAAL,CAAUjB,KAAV;AACD;;AAEOiB,EAAAA,IAAI,CAACjB,KAAD,EAA4B;AACtC,QAAI,KAAKQ,KAAL,KAAehE,KAAK,CAACkE,KAAzB,EAAgC;AAC9B,WAAKZ,QAAL;AACD;;AAED,SAAKrB,OAAL,CAAa0C,iBAAb,CAA+BnB,KAAK,CAACI,SAArC;AACD;;AAEMP,EAAAA,QAAQ,CAACuB,KAAD,EAAwB;AACrC,UAAMvB,QAAN,CAAeuB,KAAf;AACA,SAAKC,GAAL;AACD;;AAESC,EAAAA,WAAW,GAAS;AAC5B,UAAMA,WAAN;AACA,SAAKxD,wBAAL,GAAgCX,kCAAhC;AACA,SAAKS,SAAL,GAAiBX,iBAAjB;AACD;;AA7K6D", "sourcesContent": ["import { State } from '../../State';\nimport { DiagonalDirections, Directions } from '../../Directions';\nimport { AdaptedEvent, Config } from '../interfaces';\n\nimport GestureHandler from './GestureHandler';\nimport Vector from '../tools/Vector';\nimport { coneToDeviation } from '../utils';\n\nconst DEFAULT_MAX_DURATION_MS = 800;\nconst DEFAULT_MIN_VELOCITY = 700;\nconst DEFAULT_ALIGNMENT_CONE = 30;\nconst DEFAULT_DIRECTION = Directions.RIGHT;\nconst DEFAULT_NUMBER_OF_TOUCHES_REQUIRED = 1;\n\nconst AXIAL_DEVIATION_COSINE = coneToDeviation(DEFAULT_ALIGNMENT_CONE);\nconst DIAGONAL_DEVIATION_COSINE = coneToDeviation(90 - DEFAULT_ALIGNMENT_CONE);\n\nexport default class FlingGestureHandler extends GestureHandler {\n  private numberOfPointersRequired = DEFAULT_NUMBER_OF_TOUCHES_REQUIRED;\n  private direction: Directions = DEFAULT_DIRECTION;\n\n  private maxDurationMs = DEFAULT_MAX_DURATION_MS;\n  private minVelocity = DEFAULT_MIN_VELOCITY;\n  private delayTimeout!: number;\n\n  private maxNumberOfPointersSimultaneously = 0;\n  private keyPointer = NaN;\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    super.updateGestureConfig({ enabled: enabled, ...props });\n\n    if (this.config.direction) {\n      this.direction = this.config.direction;\n    }\n\n    if (this.config.numberOfPointers) {\n      this.numberOfPointersRequired = this.config.numberOfPointers;\n    }\n  }\n\n  private startFling(): void {\n    this.begin();\n\n    this.maxNumberOfPointersSimultaneously = 1;\n\n    this.delayTimeout = setTimeout(() => this.fail(), this.maxDurationMs);\n  }\n\n  private tryEndFling(): boolean {\n    const velocityVector = Vector.fromVelocity(this.tracker, this.keyPointer);\n\n    const getAlignment = (\n      direction: Directions | DiagonalDirections,\n      minimalAlignmentCosine: number\n    ) => {\n      return (\n        (direction & this.direction) === direction &&\n        velocityVector.isSimilar(\n          Vector.fromDirection(direction),\n          minimalAlignmentCosine\n        )\n      );\n    };\n\n    const axialDirectionsList = Object.values(Directions);\n    const diagonalDirectionsList = Object.values(DiagonalDirections);\n\n    // List of alignments to all activated directions\n    const axialAlignmentList = axialDirectionsList.map((direction) =>\n      getAlignment(direction, AXIAL_DEVIATION_COSINE)\n    );\n\n    const diagonalAlignmentList = diagonalDirectionsList.map((direction) =>\n      getAlignment(direction, DIAGONAL_DEVIATION_COSINE)\n    );\n\n    const isAligned =\n      axialAlignmentList.some(Boolean) || diagonalAlignmentList.some(Boolean);\n\n    const isFast = velocityVector.magnitude > this.minVelocity;\n\n    if (\n      this.maxNumberOfPointersSimultaneously ===\n        this.numberOfPointersRequired &&\n      isAligned &&\n      isFast\n    ) {\n      clearTimeout(this.delayTimeout);\n      this.activate();\n\n      return true;\n    }\n\n    return false;\n  }\n\n  private endFling() {\n    if (!this.tryEndFling()) {\n      this.fail();\n    }\n  }\n\n  protected onPointerDown(event: AdaptedEvent): void {\n    if (!this.isButtonInConfig(event.button)) {\n      return;\n    }\n\n    this.tracker.addToTracker(event);\n    this.keyPointer = event.pointerId;\n\n    super.onPointerDown(event);\n    this.newPointerAction();\n\n    this.tryToSendTouchEvent(event);\n  }\n\n  protected onPointerAdd(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerAdd(event);\n    this.newPointerAction();\n  }\n\n  private newPointerAction(): void {\n    if (this.state === State.UNDETERMINED) {\n      this.startFling();\n    }\n\n    if (this.state !== State.BEGAN) {\n      return;\n    }\n\n    this.tryEndFling();\n\n    if (\n      this.tracker.trackedPointersCount > this.maxNumberOfPointersSimultaneously\n    ) {\n      this.maxNumberOfPointersSimultaneously =\n        this.tracker.trackedPointersCount;\n    }\n  }\n\n  private pointerMoveAction(event: AdaptedEvent): void {\n    this.tracker.track(event);\n\n    if (this.state !== State.BEGAN) {\n      return;\n    }\n\n    this.tryEndFling();\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.pointerMoveAction(event);\n    super.onPointerMove(event);\n  }\n\n  protected onPointerOutOfBounds(event: AdaptedEvent): void {\n    this.pointerMoveAction(event);\n    super.onPointerOutOfBounds(event);\n  }\n\n  protected onPointerUp(event: AdaptedEvent): void {\n    super.onPointerUp(event);\n    this.onUp(event);\n\n    this.keyPointer = NaN;\n  }\n\n  protected onPointerRemove(event: AdaptedEvent): void {\n    super.onPointerRemove(event);\n    this.onUp(event);\n  }\n\n  private onUp(event: AdaptedEvent): void {\n    if (this.state === State.BEGAN) {\n      this.endFling();\n    }\n\n    this.tracker.removeFromTracker(event.pointerId);\n  }\n\n  public activate(force?: boolean): void {\n    super.activate(force);\n    this.end();\n  }\n\n  protected resetConfig(): void {\n    super.resetConfig();\n    this.numberOfPointersRequired = DEFAULT_NUMBER_OF_TOUCHES_REQUIRED;\n    this.direction = DEFAULT_DIRECTION;\n  }\n}\n"]}