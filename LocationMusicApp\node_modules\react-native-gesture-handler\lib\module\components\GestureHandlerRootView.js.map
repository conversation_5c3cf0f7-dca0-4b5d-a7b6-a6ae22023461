{"version": 3, "sources": ["GestureHandlerRootView.tsx"], "names": ["React", "View", "StyleSheet", "maybeInitializeFabric", "GestureHandlerRootViewContext", "GestureHandlerRootView", "style", "rest", "styles", "container", "create", "flex"], "mappings": ";;AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AAEA,SAASC,IAAT,EAA0BC,UAA1B,QAA4C,cAA5C;AACA,SAASC,qBAAT,QAAsC,SAAtC;AACA,OAAOC,6BAAP,MAA0C,kCAA1C;AAKA,eAAe,SAASC,sBAAT,CAAgC;AAC7CC,EAAAA,KAD6C;AAE7C,KAAGC;AAF0C,CAAhC,EAGiB;AAC9B;AACA;AACA;AACAJ,EAAAA,qBAAqB;AAErB,sBACE,oBAAC,6BAAD,CAA+B,QAA/B;AAAwC,IAAA,KAAK;AAA7C,kBACE,oBAAC,IAAD;AAAM,IAAA,KAAK,EAAEG,KAAF,aAAEA,KAAF,cAAEA,KAAF,GAAWE,MAAM,CAACC;AAA7B,KAA4CF,IAA5C,EADF,CADF;AAKD;AAED,MAAMC,MAAM,GAAGN,UAAU,CAACQ,MAAX,CAAkB;AAC/BD,EAAAA,SAAS,EAAE;AAAEE,IAAAA,IAAI,EAAE;AAAR;AADoB,CAAlB,CAAf", "sourcesContent": ["import * as React from 'react';\nimport { PropsWithChildren } from 'react';\nimport { View, ViewProps, StyleSheet } from 'react-native';\nimport { maybeInitializeFabric } from '../init';\nimport GestureHandlerRootViewContext from '../GestureHandlerRootViewContext';\n\nexport interface GestureHandlerRootViewProps\n  extends PropsWithChildren<ViewProps> {}\n\nexport default function GestureHandlerRootView({\n  style,\n  ...rest\n}: GestureHandlerRootViewProps) {\n  // Try initialize fabric on the first render, at this point we can\n  // reliably check if fabric is enabled (the function contains a flag\n  // to make sure it's called only once)\n  maybeInitializeFabric();\n\n  return (\n    <GestureHandlerRootViewContext.Provider value>\n      <View style={style ?? styles.container} {...rest} />\n    </GestureHandlerRootViewContext.Provider>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: { flex: 1 },\n});\n"]}