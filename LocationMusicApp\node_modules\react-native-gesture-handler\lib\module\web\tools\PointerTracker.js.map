{"version": 3, "sources": ["PointerTracker.ts"], "names": ["VelocityTracker", "MAX_POINTERS", "PointerTracker", "constructor", "Map", "x", "y", "lastMovedPointerId", "NaN", "i", "touchEventsIds", "set", "addToTracker", "event", "trackedPointers", "has", "pointerId", "newElement", "abosoluteCoords", "relativeCoords", "offsetX", "offsetY", "timestamp", "time", "velocityX", "velocityY", "mapTouchEventId", "cachedAbsoluteAverages", "getAbsoluteCoordsAverage", "cachedRelativeAverages", "getRelativeCoordsAverage", "removeFromTracker", "delete", "removeMappedTouchId", "track", "element", "get", "velocityTracker", "add", "velocity", "id", "mappedId", "touchId", "isNaN", "getMappedTouchEventId", "touchEventId", "key", "value", "entries", "getVelocity", "getLastAbsoluteCoords", "getLastRelativeCoords", "coordsSum", "getAbsoluteCoordsSum", "avgX", "size", "avgY", "averages", "getRelativeCoordsSum", "ignoredPointer", "sum", "for<PERSON>ach", "resetTracker", "reset", "clear", "shareCommonPointers", "stPointers", "ndPointers", "some", "includes", "trackedPointersCount", "trackedPointersIDs", "keys", "_value", "push", "_trackedPointers"], "mappings": ";;AACA,OAAOA,eAAP,MAA4B,mBAA5B;AAUA,MAAMC,YAAY,GAAG,EAArB;AAEA,eAAe,MAAMC,cAAN,CAAqB;AAc3BC,EAAAA,WAAW,GAAG;AAAA,6CAbK,IAAIH,eAAJ,EAaL;;AAAA,8CAZ4C,IAAII,GAAJ,EAY5C;;AAAA,4CAPyB,IAAIA,GAAJ,EAOzB;;AAAA;;AAAA,oDAHmB;AAAEC,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE;AAAX,KAGnB;;AAAA,oDAFmB;AAAED,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE;AAAX,KAEnB;;AACnB,SAAKC,kBAAL,GAA0BC,GAA1B;;AAEA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,YAApB,EAAkC,EAAEQ,CAApC,EAAuC;AACrC,WAAKC,cAAL,CAAoBC,GAApB,CAAwBF,CAAxB,EAA2BD,GAA3B;AACD;AACF;;AAEMI,EAAAA,YAAY,CAACC,KAAD,EAA4B;AAC7C,QAAI,KAAKC,eAAL,CAAqBC,GAArB,CAAyBF,KAAK,CAACG,SAA/B,CAAJ,EAA+C;AAC7C;AACD;;AAED,SAAKT,kBAAL,GAA0BM,KAAK,CAACG,SAAhC;AAEA,UAAMC,UAA0B,GAAG;AACjCC,MAAAA,eAAe,EAAE;AAAEb,QAAAA,CAAC,EAAEQ,KAAK,CAACR,CAAX;AAAcC,QAAAA,CAAC,EAAEO,KAAK,CAACP;AAAvB,OADgB;AAEjCa,MAAAA,cAAc,EAAE;AAAEd,QAAAA,CAAC,EAAEQ,KAAK,CAACO,OAAX;AAAoBd,QAAAA,CAAC,EAAEO,KAAK,CAACQ;AAA7B,OAFiB;AAGjCC,MAAAA,SAAS,EAAET,KAAK,CAACU,IAHgB;AAIjCC,MAAAA,SAAS,EAAE,CAJsB;AAKjCC,MAAAA,SAAS,EAAE;AALsB,KAAnC;AAQA,SAAKX,eAAL,CAAqBH,GAArB,CAAyBE,KAAK,CAACG,SAA/B,EAA0CC,UAA1C;AACA,SAAKS,eAAL,CAAqBb,KAAK,CAACG,SAA3B;AAEA,SAAKW,sBAAL,GAA8B,KAAKC,wBAAL,EAA9B;AACA,SAAKC,sBAAL,GAA8B,KAAKC,wBAAL,EAA9B;AACD;;AAEMC,EAAAA,iBAAiB,CAACf,SAAD,EAA0B;AAChD,SAAKF,eAAL,CAAqBkB,MAArB,CAA4BhB,SAA5B;AACA,SAAKiB,mBAAL,CAAyBjB,SAAzB;AACD;;AAEMkB,EAAAA,KAAK,CAACrB,KAAD,EAA4B;AACtC,UAAMsB,OAAuB,GAAG,KAAKrB,eAAL,CAAqBsB,GAArB,CAC9BvB,KAAK,CAACG,SADwB,CAAhC;;AAIA,QAAI,CAACmB,OAAL,EAAc;AACZ;AACD;;AAED,SAAK5B,kBAAL,GAA0BM,KAAK,CAACG,SAAhC;AAEA,SAAKqB,eAAL,CAAqBC,GAArB,CAAyBzB,KAAzB;AACA,UAAM,CAACW,SAAD,EAAYC,SAAZ,IAAyB,KAAKY,eAAL,CAAqBE,QAApD;AAEAJ,IAAAA,OAAO,CAACX,SAAR,GAAoBA,SAApB;AACAW,IAAAA,OAAO,CAACV,SAAR,GAAoBA,SAApB;AAEAU,IAAAA,OAAO,CAACjB,eAAR,GAA0B;AAAEb,MAAAA,CAAC,EAAEQ,KAAK,CAACR,CAAX;AAAcC,MAAAA,CAAC,EAAEO,KAAK,CAACP;AAAvB,KAA1B;AACA6B,IAAAA,OAAO,CAAChB,cAAR,GAAyB;AAAEd,MAAAA,CAAC,EAAEQ,KAAK,CAACO,OAAX;AAAoBd,MAAAA,CAAC,EAAEO,KAAK,CAACQ;AAA7B,KAAzB;AAEA,SAAKP,eAAL,CAAqBH,GAArB,CAAyBE,KAAK,CAACG,SAA/B,EAA0CmB,OAA1C;AAEA,SAAKR,sBAAL,GAA8B,KAAKC,wBAAL,EAA9B;AACA,SAAKC,sBAAL,GAA8B,KAAKC,wBAAL,EAA9B;AACD,GAzEiC,CA2ElC;;;AACQJ,EAAAA,eAAe,CAACc,EAAD,EAAmB;AACxC,SAAK,MAAM,CAACC,QAAD,EAAWC,OAAX,CAAX,IAAkC,KAAKhC,cAAvC,EAAuD;AACrD,UAAIiC,KAAK,CAACD,OAAD,CAAT,EAAoB;AAClB,aAAKhC,cAAL,CAAoBC,GAApB,CAAwB8B,QAAxB,EAAkCD,EAAlC;AACA;AACD;AACF;AACF;;AAEOP,EAAAA,mBAAmB,CAACO,EAAD,EAAmB;AAC5C,UAAMC,QAAgB,GAAG,KAAKG,qBAAL,CAA2BJ,EAA3B,CAAzB;;AACA,QAAI,CAACG,KAAK,CAACF,QAAD,CAAV,EAAsB;AACpB,WAAK/B,cAAL,CAAoBC,GAApB,CAAwB8B,QAAxB,EAAkCjC,GAAlC;AACD;AACF;;AAEMoC,EAAAA,qBAAqB,CAACC,YAAD,EAA+B;AACzD,SAAK,MAAM,CAACC,GAAD,EAAMC,KAAN,CAAX,IAA2B,KAAKrC,cAAL,CAAoBsC,OAApB,EAA3B,EAA0D;AACxD,UAAID,KAAK,KAAKF,YAAd,EAA4B;AAC1B,eAAOC,GAAP;AACD;AACF;;AAED,WAAOtC,GAAP;AACD;;AAEMyC,EAAAA,WAAW,CAACjC,SAAD,EAAoB;AAAA;;AACpC,WAAO;AACLX,MAAAA,CAAC,2BAAE,KAAKS,eAAL,CAAqBsB,GAArB,CAAyBpB,SAAzB,CAAF,0DAAE,sBAAqCQ,SADnC;AAELlB,MAAAA,CAAC,4BAAE,KAAKQ,eAAL,CAAqBsB,GAArB,CAAyBpB,SAAzB,CAAF,2DAAE,uBAAqCS;AAFnC,KAAP;AAID;;AAEMyB,EAAAA,qBAAqB,CAAClC,SAAD,EAAqB;AAAA;;AAC/C,qCAAO,KAAKF,eAAL,CAAqBsB,GAArB,CAAyBpB,SAAzB,aAAyBA,SAAzB,cAAyBA,SAAzB,GAAsC,KAAKT,kBAA3C,CAAP,2DAAO,uBACHW,eADJ;AAED;;AAEMiC,EAAAA,qBAAqB,CAACnC,SAAD,EAAqB;AAAA;;AAC/C,qCAAO,KAAKF,eAAL,CAAqBsB,GAArB,CAAyBpB,SAAzB,aAAyBA,SAAzB,cAAyBA,SAAzB,GAAsC,KAAKT,kBAA3C,CAAP,2DAAO,uBACHY,cADJ;AAED,GArHiC,CAuHlC;AACA;AACA;AACA;;;AAEOS,EAAAA,wBAAwB,GAAG;AAChC,UAAMwB,SAAS,GAAG,KAAKC,oBAAL,EAAlB;AAEA,UAAMC,IAAI,GAAGF,SAAS,CAAC/C,CAAV,GAAc,KAAKS,eAAL,CAAqByC,IAAhD;AACA,UAAMC,IAAI,GAAGJ,SAAS,CAAC9C,CAAV,GAAc,KAAKQ,eAAL,CAAqByC,IAAhD;AAEA,UAAME,QAAQ,GAAG;AACfpD,MAAAA,CAAC,EAAEsC,KAAK,CAACW,IAAD,CAAL,GAAc,KAAK3B,sBAAL,CAA4BtB,CAA1C,GAA8CiD,IADlC;AAEfhD,MAAAA,CAAC,EAAEqC,KAAK,CAACa,IAAD,CAAL,GAAc,KAAK7B,sBAAL,CAA4BrB,CAA1C,GAA8CkD;AAFlC,KAAjB;AAKA,WAAOC,QAAP;AACD;;AAEM3B,EAAAA,wBAAwB,GAAG;AAChC,UAAMsB,SAAS,GAAG,KAAKM,oBAAL,EAAlB;AAEA,UAAMJ,IAAI,GAAGF,SAAS,CAAC/C,CAAV,GAAc,KAAKS,eAAL,CAAqByC,IAAhD;AACA,UAAMC,IAAI,GAAGJ,SAAS,CAAC9C,CAAV,GAAc,KAAKQ,eAAL,CAAqByC,IAAhD;AAEA,UAAME,QAAQ,GAAG;AACfpD,MAAAA,CAAC,EAAEsC,KAAK,CAACW,IAAD,CAAL,GAAc,KAAKzB,sBAAL,CAA4BxB,CAA1C,GAA8CiD,IADlC;AAEfhD,MAAAA,CAAC,EAAEqC,KAAK,CAACa,IAAD,CAAL,GAAc,KAAK3B,sBAAL,CAA4BvB,CAA1C,GAA8CkD;AAFlC,KAAjB;AAKA,WAAOC,QAAP;AACD;;AAEMJ,EAAAA,oBAAoB,CAACM,cAAD,EAA0B;AACnD,UAAMC,GAAG,GAAG;AAAEvD,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE;AAAX,KAAZ;AAEA,SAAKQ,eAAL,CAAqB+C,OAArB,CAA6B,CAACd,KAAD,EAAQD,GAAR,KAAgB;AAC3C,UAAIA,GAAG,KAAKa,cAAZ,EAA4B;AAC1BC,QAAAA,GAAG,CAACvD,CAAJ,IAAS0C,KAAK,CAAC7B,eAAN,CAAsBb,CAA/B;AACAuD,QAAAA,GAAG,CAACtD,CAAJ,IAASyC,KAAK,CAAC7B,eAAN,CAAsBZ,CAA/B;AACD;AACF,KALD;AAOA,WAAOsD,GAAP;AACD;;AAEMF,EAAAA,oBAAoB,CAACC,cAAD,EAA0B;AACnD,UAAMC,GAAG,GAAG;AAAEvD,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE;AAAX,KAAZ;AAEA,SAAKQ,eAAL,CAAqB+C,OAArB,CAA6B,CAACd,KAAD,EAAQD,GAAR,KAAgB;AAC3C,UAAIA,GAAG,KAAKa,cAAZ,EAA4B;AAC1BC,QAAAA,GAAG,CAACvD,CAAJ,IAAS0C,KAAK,CAAC5B,cAAN,CAAqBd,CAA9B;AACAuD,QAAAA,GAAG,CAACtD,CAAJ,IAASyC,KAAK,CAAC5B,cAAN,CAAqBb,CAA9B;AACD;AACF,KALD;AAOA,WAAOsD,GAAP;AACD;;AAEME,EAAAA,YAAY,GAAS;AAC1B,SAAKzB,eAAL,CAAqB0B,KAArB;AACA,SAAKjD,eAAL,CAAqBkD,KAArB;AACA,SAAKzD,kBAAL,GAA0BC,GAA1B;;AAEA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,YAApB,EAAkC,EAAEQ,CAApC,EAAuC;AACrC,WAAKC,cAAL,CAAoBC,GAApB,CAAwBF,CAAxB,EAA2BD,GAA3B;AACD;AACF;;AAEgC,SAAnByD,mBAAmB,CAC/BC,UAD+B,EAE/BC,UAF+B,EAGtB;AACT,WAAOD,UAAU,CAACE,IAAX,CAAiBpD,SAAD,IAAemD,UAAU,CAACE,QAAX,CAAoBrD,SAApB,CAA/B,CAAP;AACD;;AAE8B,MAApBsD,oBAAoB,GAAW;AACxC,WAAO,KAAKxD,eAAL,CAAqByC,IAA5B;AACD;;AAE4B,MAAlBgB,kBAAkB,GAAG;AAC9B,UAAMC,IAAc,GAAG,EAAvB;AAEA,SAAK1D,eAAL,CAAqB+C,OAArB,CAA6B,CAACY,MAAD,EAAS3B,GAAT,KAAiB;AAC5C0B,MAAAA,IAAI,CAACE,IAAL,CAAU5B,GAAV;AACD,KAFD;AAIA,WAAO0B,IAAP;AACD;;AAEyB,MAAf1D,eAAe,GAAG;AAC3B,WAAO,KAAK6D,gBAAZ;AACD;;AAnNiC", "sourcesContent": ["import { AdaptedEvent, Point } from '../interfaces';\nimport VelocityTracker from './VelocityTracker';\n\nexport interface TrackerElement {\n  abosoluteCoords: Point;\n  relativeCoords: Point;\n  timestamp: number;\n  velocityX: number;\n  velocityY: number;\n}\n\nconst MAX_POINTERS = 20;\n\nexport default class PointerTracker {\n  private velocityTracker = new VelocityTracker();\n  private readonly _trackedPointers: Map<number, TrackerElement> = new Map<\n    number,\n    TrackerElement\n  >();\n\n  private touchEventsIds: Map<number, number> = new Map<number, number>();\n\n  private lastMovedPointerId: number;\n\n  private cachedAbsoluteAverages: Point = { x: 0, y: 0 };\n  private cachedRelativeAverages: Point = { x: 0, y: 0 };\n\n  public constructor() {\n    this.lastMovedPointerId = NaN;\n\n    for (let i = 0; i < MAX_POINTERS; ++i) {\n      this.touchEventsIds.set(i, NaN);\n    }\n  }\n\n  public addToTracker(event: AdaptedEvent): void {\n    if (this.trackedPointers.has(event.pointerId)) {\n      return;\n    }\n\n    this.lastMovedPointerId = event.pointerId;\n\n    const newElement: TrackerElement = {\n      abosoluteCoords: { x: event.x, y: event.y },\n      relativeCoords: { x: event.offsetX, y: event.offsetY },\n      timestamp: event.time,\n      velocityX: 0,\n      velocityY: 0,\n    };\n\n    this.trackedPointers.set(event.pointerId, newElement);\n    this.mapTouchEventId(event.pointerId);\n\n    this.cachedAbsoluteAverages = this.getAbsoluteCoordsAverage();\n    this.cachedRelativeAverages = this.getRelativeCoordsAverage();\n  }\n\n  public removeFromTracker(pointerId: number): void {\n    this.trackedPointers.delete(pointerId);\n    this.removeMappedTouchId(pointerId);\n  }\n\n  public track(event: AdaptedEvent): void {\n    const element: TrackerElement = this.trackedPointers.get(\n      event.pointerId\n    ) as TrackerElement;\n\n    if (!element) {\n      return;\n    }\n\n    this.lastMovedPointerId = event.pointerId;\n\n    this.velocityTracker.add(event);\n    const [velocityX, velocityY] = this.velocityTracker.velocity;\n\n    element.velocityX = velocityX;\n    element.velocityY = velocityY;\n\n    element.abosoluteCoords = { x: event.x, y: event.y };\n    element.relativeCoords = { x: event.offsetX, y: event.offsetY };\n\n    this.trackedPointers.set(event.pointerId, element);\n\n    this.cachedAbsoluteAverages = this.getAbsoluteCoordsAverage();\n    this.cachedRelativeAverages = this.getRelativeCoordsAverage();\n  }\n\n  // Mapping TouchEvents ID\n  private mapTouchEventId(id: number): void {\n    for (const [mappedId, touchId] of this.touchEventsIds) {\n      if (isNaN(touchId)) {\n        this.touchEventsIds.set(mappedId, id);\n        break;\n      }\n    }\n  }\n\n  private removeMappedTouchId(id: number): void {\n    const mappedId: number = this.getMappedTouchEventId(id);\n    if (!isNaN(mappedId)) {\n      this.touchEventsIds.set(mappedId, NaN);\n    }\n  }\n\n  public getMappedTouchEventId(touchEventId: number): number {\n    for (const [key, value] of this.touchEventsIds.entries()) {\n      if (value === touchEventId) {\n        return key;\n      }\n    }\n\n    return NaN;\n  }\n\n  public getVelocity(pointerId: number) {\n    return {\n      x: this.trackedPointers.get(pointerId)?.velocityX as number,\n      y: this.trackedPointers.get(pointerId)?.velocityY as number,\n    };\n  }\n\n  public getLastAbsoluteCoords(pointerId?: number) {\n    return this.trackedPointers.get(pointerId ?? this.lastMovedPointerId)\n      ?.abosoluteCoords as Point;\n  }\n\n  public getLastRelativeCoords(pointerId?: number) {\n    return this.trackedPointers.get(pointerId ?? this.lastMovedPointerId)\n      ?.relativeCoords as Point;\n  }\n\n  // Some handlers use these methods to send average values in native event.\n  // This may happen when pointers have already been removed from tracker (i.e. pointerup event).\n  // In situation when NaN would be sent as a response, we return cached value.\n  // That prevents handlers from crashing\n\n  public getAbsoluteCoordsAverage() {\n    const coordsSum = this.getAbsoluteCoordsSum();\n\n    const avgX = coordsSum.x / this.trackedPointers.size;\n    const avgY = coordsSum.y / this.trackedPointers.size;\n\n    const averages = {\n      x: isNaN(avgX) ? this.cachedAbsoluteAverages.x : avgX,\n      y: isNaN(avgY) ? this.cachedAbsoluteAverages.y : avgY,\n    };\n\n    return averages;\n  }\n\n  public getRelativeCoordsAverage() {\n    const coordsSum = this.getRelativeCoordsSum();\n\n    const avgX = coordsSum.x / this.trackedPointers.size;\n    const avgY = coordsSum.y / this.trackedPointers.size;\n\n    const averages = {\n      x: isNaN(avgX) ? this.cachedRelativeAverages.x : avgX,\n      y: isNaN(avgY) ? this.cachedRelativeAverages.y : avgY,\n    };\n\n    return averages;\n  }\n\n  public getAbsoluteCoordsSum(ignoredPointer?: number) {\n    const sum = { x: 0, y: 0 };\n\n    this.trackedPointers.forEach((value, key) => {\n      if (key !== ignoredPointer) {\n        sum.x += value.abosoluteCoords.x;\n        sum.y += value.abosoluteCoords.y;\n      }\n    });\n\n    return sum;\n  }\n\n  public getRelativeCoordsSum(ignoredPointer?: number) {\n    const sum = { x: 0, y: 0 };\n\n    this.trackedPointers.forEach((value, key) => {\n      if (key !== ignoredPointer) {\n        sum.x += value.relativeCoords.x;\n        sum.y += value.relativeCoords.y;\n      }\n    });\n\n    return sum;\n  }\n\n  public resetTracker(): void {\n    this.velocityTracker.reset();\n    this.trackedPointers.clear();\n    this.lastMovedPointerId = NaN;\n\n    for (let i = 0; i < MAX_POINTERS; ++i) {\n      this.touchEventsIds.set(i, NaN);\n    }\n  }\n\n  public static shareCommonPointers(\n    stPointers: number[],\n    ndPointers: number[]\n  ): boolean {\n    return stPointers.some((pointerId) => ndPointers.includes(pointerId));\n  }\n\n  public get trackedPointersCount(): number {\n    return this.trackedPointers.size;\n  }\n\n  public get trackedPointersIDs() {\n    const keys: number[] = [];\n\n    this.trackedPointers.forEach((_value, key) => {\n      keys.push(key);\n    });\n\n    return keys;\n  }\n\n  public get trackedPointers() {\n    return this._trackedPointers;\n  }\n}\n"]}