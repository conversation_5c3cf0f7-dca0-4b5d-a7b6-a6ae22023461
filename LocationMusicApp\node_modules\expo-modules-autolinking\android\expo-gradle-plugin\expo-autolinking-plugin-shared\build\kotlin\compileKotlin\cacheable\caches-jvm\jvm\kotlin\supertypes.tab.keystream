2expo.modules.plugin.AutolinkingOptions.$serializerCexpo.modules.plugin.configuration.ExpoAutolinkingConfig.$serializer;expo.modules.plugin.configuration.Configuration.$serializer7expo.modules.plugin.configuration.MavenRepo.$serializer8expo.modules.plugin.configuration.ExpoModule.$serializer9expo.modules.plugin.configuration.Publication.$serializer;expo.modules.plugin.configuration.GradleProject.$serializer:expo.modules.plugin.configuration.GradlePlugin.$serializer>expo.modules.plugin.configuration.GradleAarProject.$serializer7expo.modules.plugin.configuration.BasicMavenCredentialsCexpo.modules.plugin.configuration.BasicMavenCredentials.$serializer<expo.modules.plugin.configuration.HttpHeaderMavenCredentialsHexpo.modules.plugin.configuration.HttpHeaderMavenCredentials.$serializer5expo.modules.plugin.configuration.AWSMavenCredentialsAexpo.modules.plugin.configuration.AWSMavenCredentials.$serializer<expo.modules.plugin.configuration.MavenCredentialsSerializer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     