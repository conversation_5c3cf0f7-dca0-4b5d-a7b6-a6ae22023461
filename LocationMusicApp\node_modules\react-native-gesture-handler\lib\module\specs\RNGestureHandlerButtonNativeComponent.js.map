{"version": 3, "sources": ["RNGestureHandlerButtonNativeComponent.ts"], "names": ["codegenNativeComponent"], "mappings": "AAAA,OAAOA,sBAAP,MAAmC,yDAAnC;AAqBA,eAAeA,sBAAsB,CAAc,wBAAd,CAArC", "sourcesContent": ["import codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\nimport type {\n  Int32,\n  WithDefault,\n  Float,\n} from 'react-native/Libraries/Types/CodegenTypes';\nimport type { ViewProps, ColorValue } from 'react-native';\n\ninterface NativeProps extends ViewProps {\n  exclusive?: WithDefault<boolean, true>;\n  foreground?: boolean;\n  borderless?: boolean;\n  enabled?: WithDefault<boolean, true>;\n  rippleColor?: ColorValue;\n  rippleRadius?: Int32;\n  touchSoundDisabled?: WithDefault<boolean, false>;\n  borderWidth?: Float;\n  borderColor?: ColorValue;\n  borderStyle?: WithDefault<string, 'solid'>;\n}\n\nexport default codegenNativeComponent<NativeProps>('RNGestureHandlerButton');\n"]}