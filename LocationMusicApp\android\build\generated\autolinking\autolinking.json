{"root": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp", "reactNativePath": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\react-native", "dependencies": {"@react-native-async-storage/async-storage": {"root": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\@react-native-async-storage\\async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\@react-native-async-storage\\async-storage\\android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/Documents/augment-projects/projectduldul/LocationMusicApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\expo", "name": "expo", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\expo\\android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/Documents/augment-projects/projectduldul/LocationMusicApp/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-gesture-handler": {"root": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\react-native-gesture-handler\\android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerRootViewComponentDescriptor", "RNGestureHandlerButtonComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/Documents/augment-projects/projectduldul/LocationMusicApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-maps": {"root": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\react-native-maps", "name": "react-native-maps", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\react-native-maps\\android", "packageImportPath": "import com.rnmaps.maps.MapsPackage;", "packageInstance": "new MapsPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/Documents/augment-projects/projectduldul/LocationMusicApp/node_modules/react-native-maps/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-reanimated": {"root": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\react-native-reanimated\\android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/Documents/augment-projects/projectduldul/LocationMusicApp/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/Documents/augment-projects/projectduldul/LocationMusicApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/Documents/augment-projects/projectduldul/LocationMusicApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-track-player": {"root": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\react-native-track-player", "name": "react-native-track-player", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\node_modules\\react-native-track-player\\android", "packageImportPath": "import com.doublesymmetry.trackplayer.TrackPlayer;", "packageInstance": "new TrackPlayer()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/Documents/augment-projects/projectduldul/LocationMusicApp/node_modules/react-native-track-player/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.locationmusicapp", "sourceDir": "C:\\Users\\<USER>\\Documents\\augment-projects\\projectduldul\\LocationMusicApp\\android"}}}